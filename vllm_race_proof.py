#!/usr/bin/env python3
"""
vLLM 竞态条件漏洞证明脚本
========================

专门用于证明和演示vLLM中的竞态条件漏洞。
打印详细的请求和响应，提供清晰的漏洞证据。

核心漏洞: async_tensor_h2d异步操作导致的输出不一致
"""

import requests
import json
import time
import threading
import hashlib
from typing import List, Dict, Any
import argparse

class VLLMRaceProof:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        
        # 确定性载荷模板
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "max_tokens": 10,
            "temperature": 0.0,  # 确定性采样
            "seed": 42           # 固定种子
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def send_request(self, payload: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """发送单个请求并返回详细结果"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                resp_data = response.json()
                content = resp_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                return {
                    "request_id": request_id,
                    "success": True,
                    "status_code": response.status_code,
                    "content": content,
                    "content_hash": hashlib.md5(content.encode()).hexdigest()[:8],
                    "execution_time": execution_time,
                    "timestamp": start_time,
                    "full_response": resp_data
                }
            else:
                return {
                    "request_id": request_id,
                    "success": False,
                    "status_code": response.status_code,
                    "error": response.text,
                    "execution_time": execution_time,
                    "timestamp": start_time
                }
                
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "timestamp": start_time
            }
    
    def prove_race_condition_basic(self):
        """证明基本的竞态条件漏洞"""
        self.log("🎯 证明基本竞态条件漏洞")
        self.log("="*60)
        
        # 创建触发异步张量操作的载荷
        payload = {
            **self.base_payload,
            "messages": [{"role": "user", "content": "Count from 1 to 3:"}],
            "logit_bias": {str(i): 1.0 for i in range(10, 50)}  # 40个token的bias
        }
        
        self.log("📤 发送的载荷:")
        print(json.dumps(payload, indent=2))
        
        self.log("\n🔄 并发发送10个相同请求...")
        
        # 存储结果
        results = []
        threads = []
        
        def worker(thread_id: int):
            result = self.send_request(payload, f"basic-{thread_id}")
            results.append(result)
        
        # 启动10个并发线程
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析和打印结果
        self.analyze_and_print_results(results, "基本竞态条件测试")
    
    def prove_race_condition_advanced(self):
        """证明高级竞态条件漏洞"""
        self.log("\n🎯 证明高级竞态条件漏洞")
        self.log("="*60)
        
        # 创建更复杂的载荷，更容易触发竞态
        payload = {
            **self.base_payload,
            "messages": [{"role": "user", "content": "Generate a simple response:"}],
            "logit_bias": {
                "10": 5.0,   # 强烈偏向
                "20": -5.0,  # 强烈反对
                "30": 3.0,   # 中等偏向
                "40": -3.0,  # 中等反对
                "50": 2.0,   # 轻微偏向
                "60": -2.0   # 轻微反对
            }
        }
        
        self.log("📤 发送的载荷:")
        print(json.dumps(payload, indent=2))
        
        self.log("\n🔄 快速连续发送8个相同请求...")
        
        results = []
        
        # 快速连续发送请求
        for i in range(8):
            result = self.send_request(payload, f"advanced-{i}")
            results.append(result)
            time.sleep(0.02)  # 很短的间隔
        
        # 分析和打印结果
        self.analyze_and_print_results(results, "高级竞态条件测试")
    
    def prove_bias_interference(self):
        """证明不同bias值的相互干扰"""
        self.log("\n🎯 证明bias值相互干扰漏洞")
        self.log("="*60)
        
        # 创建三个不同的载荷，使用相同token但不同bias
        payloads = [
            {
                **self.base_payload,
                "messages": [{"role": "user", "content": "Say yes or no:"}],
                "logit_bias": {"100": 10.0}  # 强烈偏向token 100
            },
            {
                **self.base_payload,
                "messages": [{"role": "user", "content": "Say yes or no:"}],
                "logit_bias": {"100": -10.0}  # 强烈反对token 100
            },
            {
                **self.base_payload,
                "messages": [{"role": "user", "content": "Say yes or no:"}],
                "logit_bias": {"100": 0.0}  # 中性
            }
        ]
        
        self.log("📤 发送的三种不同载荷:")
        for i, payload in enumerate(payloads):
            bias_value = payload["logit_bias"]["100"]
            self.log(f"载荷{i+1} (bias=token100:{bias_value}):")
            print(json.dumps(payload, indent=2))
            print()
        
        self.log("🔄 同时发送三种不同bias的请求...")
        
        results = []
        threads = []
        
        def worker(payload_idx: int):
            result = self.send_request(payloads[payload_idx], f"bias-{payload_idx}")
            results.append((payload_idx, result))
        
        # 同时发送三种请求
        for i in range(3):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        # 按payload索引排序
        results.sort(key=lambda x: x[0])
        
        # 分析bias干扰
        self.analyze_bias_interference(results, payloads)
    
    def analyze_and_print_results(self, results: List[Dict], test_name: str):
        """分析并打印测试结果"""
        self.log(f"\n📊 {test_name} 结果分析:")
        self.log("-" * 50)
        
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"总请求数: {len(results)}")
        self.log(f"成功请求数: {len(successful_results)}")
        
        if len(successful_results) < 2:
            self.log("❌ 成功请求太少，无法分析竞态条件")
            return
        
        # 打印所有响应
        self.log("\n📥 所有响应内容:")
        for i, result in enumerate(successful_results):
            self.log(f"  响应{i+1}: '{result['content']}' (hash: {result['content_hash']}, 时间: {result['execution_time']:.3f}s)")
        
        # 检查一致性
        unique_contents = set(r['content'] for r in successful_results)
        unique_hashes = set(r['content_hash'] for r in successful_results)
        
        self.log(f"\n🔍 一致性分析:")
        self.log(f"  唯一内容数: {len(unique_contents)}")
        self.log(f"  唯一哈希数: {len(unique_hashes)}")
        
        if len(unique_contents) > 1:
            self.log("🚨 发现竞态条件漏洞证据!", "CRITICAL")
            self.log("💥 相同输入产生了不同输出:")
            for i, content in enumerate(unique_contents):
                count = sum(1 for r in successful_results if r['content'] == content)
                self.log(f"    输出{i+1}: '{content}' (出现{count}次)")
            
            self.log("\n🔬 漏洞分析:")
            self.log("  - 相同的确定性输入 (temperature=0, seed=42)")
            self.log("  - 产生了不同的输出")
            self.log("  - 证明存在竞态条件导致的非确定性")
            
        else:
            self.log("✅ 输出一致，未检测到竞态条件")
        
        # 执行时间分析
        execution_times = [r['execution_time'] for r in successful_results]
        min_time = min(execution_times)
        max_time = max(execution_times)
        avg_time = sum(execution_times) / len(execution_times)
        
        self.log(f"\n⏱️  执行时间分析:")
        self.log(f"  最短: {min_time:.3f}s")
        self.log(f"  最长: {max_time:.3f}s")
        self.log(f"  平均: {avg_time:.3f}s")
        self.log(f"  方差: {max_time - min_time:.3f}s")
        
        if max_time - min_time > 0.5:
            self.log("⚠️  执行时间方差较大，可能存在异步操作问题")
    
    def analyze_bias_interference(self, results: List[tuple], payloads: List[Dict]):
        """分析bias值相互干扰"""
        self.log("\n📊 bias干扰分析:")
        self.log("-" * 50)
        
        self.log("📥 各bias值的响应:")
        for payload_idx, result in results:
            bias_value = payloads[payload_idx]["logit_bias"]["100"]
            if result.get("success", False):
                self.log(f"  bias={bias_value:5.1f}: '{result['content']}' (hash: {result['content_hash']})")
            else:
                self.log(f"  bias={bias_value:5.1f}: ERROR - {result.get('error', 'Unknown error')}")
        
        # 检查是否有异常模式
        successful_results = [(idx, r) for idx, r in results if r.get("success", False)]
        
        if len(successful_results) < 3:
            self.log("❌ 成功请求太少，无法分析bias干扰")
            return
        
        # 分析输出模式
        outputs_by_bias = {}
        for payload_idx, result in successful_results:
            bias_value = payloads[payload_idx]["logit_bias"]["100"]
            outputs_by_bias[bias_value] = result['content']
        
        unique_outputs = set(outputs_by_bias.values())
        
        self.log(f"\n🔍 bias干扰分析:")
        self.log(f"  不同bias值: {len(outputs_by_bias)}")
        self.log(f"  唯一输出: {len(unique_outputs)}")
        
        if len(unique_outputs) < len(outputs_by_bias):
            self.log("🚨 发现bias干扰漏洞证据!", "CRITICAL")
            self.log("💥 不同bias值产生了相同输出:")
            
            # 找出相同输出的bias值
            output_groups = {}
            for bias, output in outputs_by_bias.items():
                if output not in output_groups:
                    output_groups[output] = []
                output_groups[output].append(bias)
            
            for output, biases in output_groups.items():
                if len(biases) > 1:
                    self.log(f"    输出 '{output}' 来自bias值: {biases}")
            
            self.log("\n🔬 漏洞分析:")
            self.log("  - 不同的bias值应该产生不同的输出")
            self.log("  - 但实际产生了相同的输出")
            self.log("  - 证明存在异步操作的相互干扰")
        else:
            self.log("✅ 不同bias值产生了不同输出，符合预期")
    
    def run_comprehensive_proof(self):
        """运行全面的漏洞证明"""
        self.log("🚀 vLLM 竞态条件漏洞证明")
        self.log("="*70)
        self.log("基于源代码分析的漏洞位置: vllm/v1/sample/sampler.py:258-262")
        self.log("核心问题: async_tensor_h2d + index_put_ 异步操作竞态")
        self.log("="*70)
        
        # 运行三个证明测试
        self.prove_race_condition_basic()
        self.prove_race_condition_advanced()
        self.prove_bias_interference()
        
        self.log("\n" + "="*70)
        self.log("🎯 漏洞证明总结")
        self.log("="*70)
        self.log("如果上述测试中出现:")
        self.log("1. 🚨 相同输入产生不同输出 → 证明存在竞态条件")
        self.log("2. 🚨 不同bias值产生相同输出 → 证明存在异步干扰")
        self.log("3. ⚠️  执行时间方差过大 → 证明存在同步问题")
        self.log("\n则确认vLLM存在竞态条件漏洞!")
        self.log("\n🛠️  修复方案: 在async_tensor_h2d后添加torch.cuda.synchronize()")

def main():
    parser = argparse.ArgumentParser(description='vLLM竞态条件漏洞证明工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    
    args = parser.parse_args()
    
    prover = VLLMRaceProof(args.target)
    prover.run_comprehensive_proof()

if __name__ == "__main__":
    main()
