#!/usr/bin/env python3
"""
Secure implementation to fix the URL Authentication Bypass vulnerability in vLLM
This shows how to properly validate URLs to prevent SSRF attacks.
"""

import ipaddress
import socket
from urllib.parse import urlparse
from typing import Set, List

class SecureURLValidator:
    """Secure URL validator that prevents SSRF attacks"""
    
    def __init__(self):
        # Define blocked IP ranges
        self.blocked_networks = [
            ipaddress.ip_network('*********/8'),      # Loopback
            ipaddress.ip_network('10.0.0.0/8'),       # Private Class A
            ipaddress.ip_network('**********/12'),    # Private Class B  
            ipaddress.ip_network('***********/16'),   # Private Class C
            ipaddress.ip_network('***********/16'),   # Link-local (AWS metadata)
            ipaddress.ip_network('*********/4'),      # Multicast
            ipaddress.ip_network('240.0.0.0/4'),      # Reserved
            ipaddress.ip_network('::1/128'),          # IPv6 loopback
            ipaddress.ip_network('fc00::/7'),         # IPv6 private
            ipaddress.ip_network('fe80::/10'),        # IPv6 link-local
        ]
        
        # Define blocked hostnames
        self.blocked_hostnames = {
            'localhost',
            'metadata.google.internal',
            'metadata',
            'instance-data',
        }
        
        # Define allowed schemes
        self.allowed_schemes = {'http', 'https'}
    
    def _is_ip_blocked(self, ip_str: str) -> bool:
        """Check if an IP address is in blocked ranges"""
        try:
            ip = ipaddress.ip_address(ip_str)
            for network in self.blocked_networks:
                if ip in network:
                    return True
            return False
        except ValueError:
            return False
    
    def _resolve_hostname(self, hostname: str) -> List[str]:
        """Resolve hostname to IP addresses"""
        try:
            # Get all IP addresses for the hostname
            result = socket.getaddrinfo(hostname, None)
            ips = [info[4][0] for info in result]
            return list(set(ips))  # Remove duplicates
        except socket.gaierror:
            raise ValueError(f"Cannot resolve hostname: {hostname}")
    
    def _validate_hostname(self, hostname: str) -> None:
        """Validate hostname and its resolved IPs"""
        if not hostname:
            raise ValueError("Empty hostname not allowed")
        
        # Check blocked hostnames
        if hostname.lower() in self.blocked_hostnames:
            raise ValueError(f"Blocked hostname: {hostname}")
        
        # Check if hostname is an IP address
        try:
            if self._is_ip_blocked(hostname):
                raise ValueError(f"Blocked IP address: {hostname}")
        except ValueError:
            pass  # Not an IP, continue with hostname resolution
        
        # Resolve hostname and check all IPs
        try:
            resolved_ips = self._resolve_hostname(hostname)
            for ip in resolved_ips:
                if self._is_ip_blocked(ip):
                    raise ValueError(f"Hostname {hostname} resolves to blocked IP: {ip}")
        except ValueError as e:
            if "Cannot resolve hostname" in str(e):
                raise e
            raise ValueError(f"Hostname validation failed: {e}")
    
    def validate_http_url(self, url: str) -> None:
        """
        Secure URL validation that prevents SSRF attacks
        
        This replaces the vulnerable vLLM _validate_http_url() method
        """
        if not url:
            raise ValueError("Empty URL not allowed")
        
        # Parse URL
        try:
            parsed_url = urlparse(url)
        except Exception as e:
            raise ValueError(f"Invalid URL format: {e}")
        
        # Validate scheme
        if parsed_url.scheme not in self.allowed_schemes:
            raise ValueError(f"Invalid scheme: {parsed_url.scheme}. "
                           f"Allowed schemes: {', '.join(self.allowed_schemes)}")
        
        # Extract hostname (this handles the @ bypass automatically)
        hostname = parsed_url.hostname
        if not hostname:
            raise ValueError("URL must contain a valid hostname")
        
        # Validate hostname and resolved IPs
        self._validate_hostname(hostname)
        
        # Additional checks for port
        port = parsed_url.port
        if port is not None:
            if port < 1 or port > 65535:
                raise ValueError(f"Invalid port: {port}")
            
            # Block common internal service ports
            blocked_ports = {22, 23, 25, 53, 135, 139, 445, 1433, 1521, 3306, 3389, 5432, 6379, 27017}
            if port in blocked_ports:
                raise ValueError(f"Blocked port: {port}")

def demonstrate_fix():
    """Demonstrate how the fix prevents the bypass attacks"""
    
    validator = SecureURLValidator()
    
    print("🛡️  Testing Secure URL Validator")
    print("=" * 50)
    print()
    
    # Test cases that should be blocked
    malicious_urls = [
        "https://<EMAIL>/malware.exe",
        "https://google.com@127.0.0.1/",
        "https://trusted.com@localhost:8080/",
        "https://amazon.com@***************/metadata/",
        "https://example.com@***********/admin",
        "http://youtube.com@********:22/",
        "https://metadata.google.internal/",
        "http://127.0.0.1:6379/",
    ]
    
    print("🚨 Testing malicious URLs (should be BLOCKED):")
    for url in malicious_urls:
        try:
            validator.validate_http_url(url)
            print(f"   ❌ FAILED: {url} - Should have been blocked!")
        except ValueError as e:
            print(f"   ✅ BLOCKED: {url}")
            print(f"      Reason: {e}")
        print()
    
    # Test cases that should be allowed
    legitimate_urls = [
        "https://www.google.com/images/logo.png",
        "http://example.com/image.jpg", 
        "https://cdn.jsdelivr.net/npm/package/image.png",
        "https://raw.githubusercontent.com/user/repo/main/image.jpg",
    ]
    
    print("✅ Testing legitimate URLs (should be ALLOWED):")
    for url in legitimate_urls:
        try:
            validator.validate_http_url(url)
            print(f"   ✅ ALLOWED: {url}")
        except ValueError as e:
            print(f"   ❌ BLOCKED: {url} - {e}")
        print()

def show_vllm_patch():
    """Show the actual patch for vLLM"""
    
    print("🔧 vLLM Patch Implementation:")
    print("=" * 40)
    print()
    
    print("File: vllm/connections.py")
    print()
    print("BEFORE (vulnerable):")
    print("""
def _validate_http_url(self, url: str):
    parsed_url = urlparse(url)
    
    if parsed_url.scheme not in ("http", "https"):
        raise ValueError("Invalid HTTP URL: A valid HTTP URL "
                         "must have scheme 'http' or 'https'.")
    # ❌ NO HOSTNAME VALIDATION!
""")
    
    print("AFTER (secure):")
    print("""
def _validate_http_url(self, url: str):
    parsed_url = urlparse(url)
    
    # Validate scheme
    if parsed_url.scheme not in ("http", "https"):
        raise ValueError("Invalid HTTP URL: A valid HTTP URL "
                         "must have scheme 'http' or 'https'.")
    
    # ✅ ADD HOSTNAME VALIDATION
    hostname = parsed_url.hostname
    if not hostname:
        raise ValueError("URL must contain a valid hostname")
    
    # Block internal IP ranges
    blocked_networks = [
        '*********/8', '10.0.0.0/8', '**********/12', 
        '***********/16', '***********/16'
    ]
    
    # Check if hostname is blocked IP
    try:
        import ipaddress
        ip = ipaddress.ip_address(hostname)
        for network in blocked_networks:
            if ip in ipaddress.ip_network(network):
                raise ValueError(f"Blocked IP address: {hostname}")
    except ValueError as e:
        if "Blocked IP" in str(e):
            raise e
        # Not an IP, check hostname resolution
        import socket
        try:
            resolved_ips = socket.getaddrinfo(hostname, None)
            for info in resolved_ips:
                ip = ipaddress.ip_address(info[4][0])
                for network in blocked_networks:
                    if ip in ipaddress.ip_network(network):
                        raise ValueError(f"Hostname resolves to blocked IP: {info[4][0]}")
        except socket.gaierror:
            raise ValueError(f"Cannot resolve hostname: {hostname}")
""")

if __name__ == "__main__":
    demonstrate_fix()
    print()
    show_vllm_patch()
