#!/usr/bin/env python3
"""
vLLM logit_bias 竞态条件测试
===========================

专门测试logit_bias在竞态条件下的失效或异常行为。
基于用户提供的实际案例进行测试。

目标: 证明logit_bias在并发情况下不生效或生效异常
"""

import requests
import json
import time
import threading
from typing import List, Dict, Any
import argparse

class LogitBiasRaceTester:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        
        # 基础配置
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "max_tokens": 50,
            "temperature": 0.0,
            "seed": 42
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def send_request_with_full_response(self, payload: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """发送请求并返回完整响应"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                resp_data = response.json()
                content = resp_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                return {
                    "request_id": request_id,
                    "success": True,
                    "status_code": response.status_code,
                    "content": content,
                    "full_response": resp_data,
                    "execution_time": execution_time,
                    "timestamp": start_time
                }
            else:
                return {
                    "request_id": request_id,
                    "success": False,
                    "status_code": response.status_code,
                    "error": response.text,
                    "execution_time": execution_time,
                    "timestamp": start_time
                }
                
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "timestamp": start_time
            }
    
    def test_france_capital_bias_race(self):
        """测试法国首都问题的logit_bias竞态条件"""
        self.log("🎯 测试法国首都问题的logit_bias竞态条件")
        self.log("="*60)
        
        # 基于用户提供的案例
        base_payload = {
            **self.base_payload,
            "messages": [
                {
                    "role": "user",
                    "content": "What is the capital of France?"
                }
            ]
        }
        
        # 测试不同的logit_bias配置
        test_cases = [
            {
                "name": "强制错误答案",
                "logit_bias": {
                    "59604": -100,  # 可能是"Paris"相关token
                    "12095": -100,  # 可能是"Paris"相关token  
                    "39572": 5      # 可能是"London"相关token
                },
                "expected": "应该避免Paris，偏向London"
            },
            {
                "name": "无bias对照",
                "logit_bias": {},
                "expected": "正常回答Paris"
            },
            {
                "name": "强制正确答案",
                "logit_bias": {
                    "59604": 10,    # 强化Paris
                    "12095": 10,    # 强化Paris
                    "39572": -100   # 避免London
                },
                "expected": "应该强烈偏向Paris"
            }
        ]
        
        self.log("📤 测试案例:")
        for i, case in enumerate(test_cases):
            self.log(f"   案例{i+1}: {case['name']}")
            self.log(f"      logit_bias: {case['logit_bias']}")
            self.log(f"      预期: {case['expected']}")
        
        # 并发测试每个案例
        all_results = []
        
        for case in test_cases:
            self.log(f"\n🔄 并发测试: {case['name']}")
            
            payload = {**base_payload, "logit_bias": case["logit_bias"]}
            
            # 打印完整载荷
            self.log("📤 发送载荷:")
            print(json.dumps(payload, indent=2))
            
            # 并发发送5个相同请求
            case_results = []
            threads = []
            
            def worker(thread_id: int):
                result = self.send_request_with_full_response(payload, f"{case['name']}-{thread_id}")
                case_results.append(result)
            
            for i in range(5):
                thread = threading.Thread(target=worker, args=(i,))
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            # 分析结果
            self.analyze_case_results(case, case_results)
            all_results.append((case, case_results))
        
        # 跨案例分析
        self.analyze_cross_case_race_condition(all_results)
    
    def analyze_case_results(self, case: Dict, results: List[Dict]):
        """分析单个案例的结果"""
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"\n📊 {case['name']} 结果分析:")
        self.log(f"   成功请求: {len(successful_results)}/{len(results)}")
        
        if not successful_results:
            self.log("❌ 没有成功的请求")
            return
        
        # 打印所有响应
        self.log("\n📥 所有响应:")
        unique_contents = set()
        
        for i, result in enumerate(successful_results):
            content = result['content']
            unique_contents.add(content)
            
            self.log(f"   响应{i+1}: '{content}'")
            self.log(f"      执行时间: {result['execution_time']:.3f}s")
            
            # 打印完整响应
            if i == 0:  # 只打印第一个完整响应避免过多输出
                self.log(f"      完整响应:")
                print(json.dumps(result['full_response'], indent=2))
        
        # 分析一致性
        self.log(f"\n🔍 一致性分析:")
        self.log(f"   唯一内容数: {len(unique_contents)}")
        
        if len(unique_contents) > 1:
            self.log("🚨 发现输出不一致!", "CRITICAL")
            for i, content in enumerate(unique_contents):
                count = sum(1 for r in successful_results if r['content'] == content)
                self.log(f"      输出{i+1}: '{content}' (出现{count}次)")
        else:
            self.log("✅ 输出一致")
        
        # 分析logit_bias效果
        self.analyze_logit_bias_effectiveness(case, successful_results)
    
    def analyze_logit_bias_effectiveness(self, case: Dict, results: List[Dict]):
        """分析logit_bias的有效性"""
        if not results:
            return
        
        self.log(f"\n🔬 logit_bias效果分析:")
        
        # 检查是否包含关键词
        contents = [r['content'] for r in results]
        
        paris_mentions = sum(1 for content in contents if 'Paris' in content)
        london_mentions = sum(1 for content in contents if 'London' in content)
        
        self.log(f"   提到Paris: {paris_mentions}/{len(contents)}")
        self.log(f"   提到London: {london_mentions}/{len(contents)}")
        
        # 根据案例分析效果
        case_name = case['name']
        logit_bias = case['logit_bias']
        
        if case_name == "强制错误答案":
            # 应该避免Paris，偏向London
            if paris_mentions > 0:
                self.log("🚨 logit_bias失效: 仍然提到了Paris!", "CRITICAL")
            if london_mentions == 0:
                self.log("⚠️  logit_bias部分失效: 没有偏向London")
            if london_mentions > 0 and paris_mentions == 0:
                self.log("✅ logit_bias生效: 成功避免Paris并偏向London")
                
        elif case_name == "强制正确答案":
            # 应该强化Paris，避免London
            if london_mentions > 0:
                self.log("🚨 logit_bias失效: 仍然提到了London!", "CRITICAL")
            if paris_mentions == 0:
                self.log("⚠️  logit_bias失效: 没有偏向Paris")
            if paris_mentions > 0 and london_mentions == 0:
                self.log("✅ logit_bias生效: 成功偏向Paris并避免London")
                
        elif case_name == "无bias对照":
            # 正常情况应该回答Paris
            if paris_mentions == 0:
                self.log("⚠️  异常: 无bias情况下没有提到Paris")
            else:
                self.log("✅ 正常: 无bias情况下正确回答Paris")
    
    def analyze_cross_case_race_condition(self, all_results: List[tuple]):
        """分析跨案例的竞态条件"""
        self.log(f"\n🔍 跨案例竞态条件分析:")
        self.log("="*60)
        
        # 收集所有案例的结果
        case_outputs = {}
        
        for case, results in all_results:
            successful_results = [r for r in results if r.get("success", False)]
            if successful_results:
                contents = [r['content'] for r in successful_results]
                case_outputs[case['name']] = {
                    'contents': contents,
                    'unique_contents': set(contents),
                    'logit_bias': case['logit_bias']
                }
        
        # 检查是否有异常的相似性
        self.log("📊 各案例输出对比:")
        
        case_names = list(case_outputs.keys())
        for i, case1 in enumerate(case_names):
            for j, case2 in enumerate(case_names):
                if i < j:
                    contents1 = case_outputs[case1]['unique_contents']
                    contents2 = case_outputs[case2]['unique_contents']
                    
                    # 检查是否有相同输出
                    common_outputs = contents1.intersection(contents2)
                    
                    if common_outputs:
                        self.log(f"🚨 发现跨案例相同输出!", "CRITICAL")
                        self.log(f"   案例1: {case1}")
                        self.log(f"   案例2: {case2}")
                        self.log(f"   相同输出: {list(common_outputs)}")
                        self.log(f"   这可能表明logit_bias在并发情况下相互干扰!")
        
        # 检查logit_bias失效模式
        self.log(f"\n🔬 logit_bias失效模式分析:")
        
        for case_name, data in case_outputs.items():
            self.log(f"   {case_name}:")
            self.log(f"      logit_bias: {data['logit_bias']}")
            self.log(f"      输出数量: {len(data['unique_contents'])}")
            
            # 简单的效果检查
            has_paris = any('Paris' in content for content in data['unique_contents'])
            has_london = any('London' in content for content in data['unique_contents'])
            
            self.log(f"      包含Paris: {has_paris}")
            self.log(f"      包含London: {has_london}")
    
    def test_rapid_bias_switching(self):
        """测试快速切换logit_bias的竞态条件"""
        self.log(f"\n🎯 测试快速切换logit_bias竞态条件")
        self.log("="*60)
        
        base_payload = {
            **self.base_payload,
            "messages": [
                {
                    "role": "user", 
                    "content": "What is the capital of France?"
                }
            ]
        }
        
        # 快速切换的bias配置
        bias_configs = [
            {"59604": -100, "39572": 10},   # 避免Paris，偏向London
            {"59604": 10, "39572": -100},   # 偏向Paris，避免London
            {},                             # 无bias
        ]
        
        self.log("🔄 快速连续发送不同bias配置的请求...")
        
        results = []
        
        for i in range(9):  # 发送9个请求，每3个一组
            bias_idx = i % 3
            payload = {**base_payload, "logit_bias": bias_configs[bias_idx]}
            
            self.log(f"   请求{i+1}: bias={bias_configs[bias_idx]}")
            
            result = self.send_request_with_full_response(payload, f"rapid-{i}")
            results.append((bias_idx, result))
            
            time.sleep(0.05)  # 50ms间隔，增加竞态可能性
        
        # 分析快速切换结果
        self.analyze_rapid_switching_results(results, bias_configs)
    
    def analyze_rapid_switching_results(self, results: List[tuple], bias_configs: List[Dict]):
        """分析快速切换的结果"""
        self.log(f"\n📊 快速切换结果分析:")
        
        # 按bias配置分组
        groups = {0: [], 1: [], 2: []}
        
        for bias_idx, result in results:
            if result.get("success", False):
                groups[bias_idx].append(result)
        
        # 分析每组结果
        for bias_idx, group_results in groups.items():
            if not group_results:
                continue
                
            bias_config = bias_configs[bias_idx]
            self.log(f"\n   Bias配置{bias_idx}: {bias_config}")
            self.log(f"   成功请求: {len(group_results)}")
            
            contents = [r['content'] for r in group_results]
            unique_contents = set(contents)
            
            self.log(f"   唯一输出: {len(unique_contents)}")
            
            for content in unique_contents:
                count = contents.count(content)
                self.log(f"      '{content}' (出现{count}次)")
        
        # 检查是否有异常模式
        all_contents_by_bias = {}
        for bias_idx, group_results in groups.items():
            if group_results:
                all_contents_by_bias[bias_idx] = set(r['content'] for r in group_results)
        
        # 检查不同bias配置是否产生了相同输出
        bias_indices = list(all_contents_by_bias.keys())
        for i in range(len(bias_indices)):
            for j in range(i+1, len(bias_indices)):
                bias1, bias2 = bias_indices[i], bias_indices[j]
                contents1 = all_contents_by_bias[bias1]
                contents2 = all_contents_by_bias[bias2]
                
                common = contents1.intersection(contents2)
                if common:
                    self.log(f"🚨 发现快速切换竞态条件!", "CRITICAL")
                    self.log(f"   Bias配置{bias1}和{bias2}产生了相同输出:")
                    self.log(f"   {list(common)}")
    
    def run_comprehensive_logit_bias_race_test(self):
        """运行全面的logit_bias竞态条件测试"""
        self.log("🚀 vLLM logit_bias 竞态条件测试")
        self.log("="*70)
        self.log("目标: 证明logit_bias在并发情况下失效或异常")
        self.log("="*70)
        
        # 测试1: 法国首都问题的bias竞态
        self.test_france_capital_bias_race()
        
        # 测试2: 快速切换bias的竞态
        self.test_rapid_bias_switching()
        
        self.log("\n" + "="*70)
        self.log("🎯 logit_bias竞态条件测试总结")
        self.log("="*70)
        self.log("如果发现以下情况，说明存在logit_bias竞态条件:")
        self.log("1. 🚨 相同logit_bias配置产生不同输出")
        self.log("2. 🚨 不同logit_bias配置产生相同输出") 
        self.log("3. 🚨 logit_bias效果与预期不符")
        self.log("4. 🚨 快速切换时bias效果混乱")
        self.log("\n💡 这些都是async_tensor_h2d竞态条件的表现!")

def main():
    parser = argparse.ArgumentParser(description='vLLM logit_bias竞态条件测试工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    
    args = parser.parse_args()
    
    tester = LogitBiasRaceTester(args.target)
    tester.run_comprehensive_logit_bias_race_test()

if __name__ == "__main__":
    main()
