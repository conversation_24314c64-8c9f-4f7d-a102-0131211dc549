{"model": "Qwen/Qwen2.5-VL-3B-Instruct", "messages": [{"role": "user", "content": "Hello"}], "guided_json": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "object", "properties": {"nested": {"type": "string"}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}, "max_tokens": 5}