#!/usr/bin/env python3
"""
vLLM 逻辑竞态条件测试脚本
========================

专门测试导致相同输入产生不同输出的逻辑竞态条件。
基于源代码分析发现的真实漏洞。

核心问题: async_tensor_h2d + index_put_ 的异步操作竞态
位置: vllm/v1/sample/sampler.py:258-262
"""

import requests
import json
import time
import threading
import hashlib
from typing import List, Dict, Any, Tuple
import argparse
from dataclasses import dataclass

@dataclass
class ConsistencyTestResult:
    """一致性测试结果"""
    request_id: str
    input_hash: str
    output_content: str
    output_hash: str
    execution_time: float
    timestamp: float
    success: bool

class VLLMLogicRaceTester:
    def __init__(self, target_url: str, debug: bool = False):
        self.target_url = target_url.rstrip('/')
        self.debug = debug
        
        # 基础载荷模板
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "max_tokens": 10,
            "temperature": 0.0,  # 确定性采样
            "seed": 42           # 固定种子
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def create_deterministic_payload(self, prompt: str, logit_bias: Dict[str, float]) -> Dict[str, Any]:
        """创建确定性测试载荷"""
        return {
            **self.base_payload,
            "messages": [{"role": "user", "content": prompt}],
            "logit_bias": logit_bias
        }
    
    def send_single_request(self, payload: Dict[str, Any], request_id: str) -> ConsistencyTestResult:
        """发送单个请求并记录结果"""
        # 计算输入哈希
        input_str = json.dumps(payload, sort_keys=True)
        input_hash = hashlib.md5(input_str.encode()).hexdigest()[:8]
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                resp_data = response.json()
                content = resp_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                output_hash = hashlib.md5(content.encode()).hexdigest()[:8]
                
                return ConsistencyTestResult(
                    request_id=request_id,
                    input_hash=input_hash,
                    output_content=content,
                    output_hash=output_hash,
                    execution_time=execution_time,
                    timestamp=start_time,
                    success=True
                )
            else:
                return ConsistencyTestResult(
                    request_id=request_id,
                    input_hash=input_hash,
                    output_content=f"ERROR: {response.status_code}",
                    output_hash="error",
                    execution_time=execution_time,
                    timestamp=start_time,
                    success=False
                )
                
        except Exception as e:
            return ConsistencyTestResult(
                request_id=request_id,
                input_hash=input_hash,
                output_content=f"EXCEPTION: {str(e)}",
                output_hash="exception",
                execution_time=time.time() - start_time,
                timestamp=start_time,
                success=False
            )
    
    def test_async_tensor_race_consistency(self) -> Dict[str, Any]:
        """测试异步张量操作导致的输出不一致"""
        self.log("🎯 测试异步张量竞态导致的输出不一致...")

        # 创建触发异步张量操作的载荷
        # 使用重叠的token ID来增加竞态可能性
        test_cases = [
            {
                "name": "overlapping_tokens",
                "prompt": "Count to 5:",
                "logit_bias": {str(i): 2.0 for i in range(10, 20)}  # 重叠token
            },
            {
                "name": "sequential_tokens",
                "prompt": "Say hello:",
                "logit_bias": {str(i): 1.0 for i in range(50, 100)}  # 顺序token
            },
            {
                "name": "mixed_bias",
                "prompt": "Generate text:",
                "logit_bias": {"10": 5.0, "20": -5.0, "30": 3.0, "40": -3.0}  # 混合bias
            }
        ]

        results = {}

        for test_case in test_cases:
            self.log(f"   测试案例: {test_case['name']}")

            payload = self.create_deterministic_payload(
                test_case["prompt"],
                test_case["logit_bias"]
            )

            # 打印测试载荷
            self.log(f"   📤 发送载荷: {json.dumps(payload, indent=2)}")

            # 并发发送相同请求
            case_results = []
            threads = []

            def worker(thread_id: int):
                result = self.send_single_request(payload, f"{test_case['name']}-{thread_id}")
                case_results.append(result)

            # 启动8个并发线程发送相同请求 (增加并发数)
            for i in range(8):
                thread = threading.Thread(target=worker, args=(i,))
                threads.append(thread)
                thread.start()

            # 等待所有线程完成
            for thread in threads:
                thread.join()

            # 打印所有响应
            self.log(f"   📥 收到 {len(case_results)} 个响应:")
            for i, result in enumerate(case_results):
                if result.success:
                    self.log(f"      响应{i+1}: '{result.output_content}' (hash: {result.output_hash})")
                else:
                    self.log(f"      响应{i+1}: ERROR - {result.output_content}")

            # 分析一致性
            successful_results = [r for r in case_results if r.success]

            if len(successful_results) < 2:
                results[test_case['name']] = {
                    "consistency": "insufficient_data",
                    "unique_outputs": 0,
                    "total_requests": len(case_results),
                    "successful_requests": len(successful_results)
                }
                continue

            # 检查输出一致性
            unique_outputs = set(r.output_hash for r in successful_results)
            unique_contents = set(r.output_content for r in successful_results)

            is_consistent = len(unique_outputs) == 1

            results[test_case['name']] = {
                "consistency": "consistent" if is_consistent else "inconsistent",
                "unique_outputs": len(unique_outputs),
                "unique_contents": list(unique_contents),
                "output_hashes": list(unique_outputs),
                "total_requests": len(case_results),
                "successful_requests": len(successful_results),
                "execution_times": [r.execution_time for r in successful_results],
                "race_condition_detected": not is_consistent,
                "all_responses": [r.output_content for r in successful_results]
            }

            if not is_consistent:
                self.log(f"🚨 发现输出不一致! 案例: {test_case['name']}", "CRITICAL")
                self.log(f"   📊 统计: {len(unique_outputs)} 种不同输出，共 {len(successful_results)} 个成功请求")
                for i, content in enumerate(unique_contents):
                    self.log(f"   🔍 唯一输出{i+1}: '{content}'")
            else:
                self.log(f"   ✅ 输出一致: '{list(unique_contents)[0]}'")

        return results
    
    def test_logit_bias_accumulation_race(self) -> Dict[str, Any]:
        """测试logit_bias累加操作的竞态条件"""
        self.log("🎯 测试logit_bias累加竞态条件...")

        # 使用相同token ID但不同bias值的请求
        base_prompt = "Generate a number:"

        test_payloads = [
            self.create_deterministic_payload(base_prompt, {"15": 10.0}),   # 强烈偏向token 15
            self.create_deterministic_payload(base_prompt, {"15": -10.0}),  # 强烈反对token 15
            self.create_deterministic_payload(base_prompt, {"15": 5.0}),    # 中等偏向token 15
        ]

        # 打印测试载荷
        for i, payload in enumerate(test_payloads):
            bias_value = list(payload["logit_bias"].values())[0]
            self.log(f"   📤 载荷{i+1} (bias={bias_value}): {json.dumps(payload, indent=2)}")

        results = []

        def send_payload(payload_idx: int):
            result = self.send_single_request(test_payloads[payload_idx], f"accumulation-{payload_idx}")
            results.append((payload_idx, result))

        # 同时发送不同bias的请求
        threads = []
        for i in range(3):
            thread = threading.Thread(target=send_payload, args=(i,))
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        # 打印所有响应
        self.log("   📥 收到响应:")
        for payload_idx, result in results:
            bias_value = list(test_payloads[payload_idx]["logit_bias"].values())[0]
            if result.success:
                self.log(f"      bias={bias_value}: '{result.output_content}' (hash: {result.output_hash})")
            else:
                self.log(f"      bias={bias_value}: ERROR - {result.output_content}")

        # 分析结果
        successful_results = [(idx, r) for idx, r in results if r.success]

        if len(successful_results) < 3:
            return {
                "accumulation_race_detected": False,
                "reason": "insufficient_successful_requests",
                "successful_count": len(successful_results)
            }

        # 检查是否有异常的输出模式
        outputs_by_bias = {}
        for payload_idx, result in successful_results:
            bias_value = list(test_payloads[payload_idx]["logit_bias"].values())[0]
            outputs_by_bias[bias_value] = result.output_content

        # 理论上，不同bias应该产生不同的输出
        unique_outputs = set(outputs_by_bias.values())
        expected_different = len(outputs_by_bias) == len(unique_outputs)

        # 详细分析
        self.log(f"   📊 分析结果:")
        self.log(f"      不同bias值: {len(outputs_by_bias)}")
        self.log(f"      唯一输出: {len(unique_outputs)}")
        self.log(f"      预期不同: {expected_different}")

        if not expected_different:
            self.log(f"   🚨 竞态条件证据: 不同bias值产生了相同输出!", "CRITICAL")
            # 找出相同的输出
            output_counts = {}
            for bias, output in outputs_by_bias.items():
                if output not in output_counts:
                    output_counts[output] = []
                output_counts[output].append(bias)

            for output, biases in output_counts.items():
                if len(biases) > 1:
                    self.log(f"      相同输出 '{output}' 来自不同bias: {biases}")

        return {
            "accumulation_race_detected": not expected_different,
            "outputs_by_bias": outputs_by_bias,
            "unique_outputs": len(unique_outputs),
            "expected_different_outputs": len(outputs_by_bias),
            "bias_values": list(outputs_by_bias.keys()),
            "detailed_analysis": {
                "output_counts": {output: [bias for bias, out in outputs_by_bias.items() if out == output]
                                for output in unique_outputs}
            }
        }
    
    def test_device_synchronization_race(self) -> Dict[str, Any]:
        """测试设备同步竞态条件"""
        self.log("🎯 测试设备同步竞态条件...")

        # 创建需要大量异步操作的载荷
        large_logit_bias = {str(i): float(i % 5) for i in range(100)}

        payload = self.create_deterministic_payload(
            "Generate consistent output:",
            large_logit_bias
        )

        # 打印测试载荷
        self.log(f"   📤 发送载荷 (包含{len(large_logit_bias)}个logit_bias):")
        self.log(f"      prompt: '{payload['messages'][0]['content']}'")
        self.log(f"      logit_bias样例: {dict(list(large_logit_bias.items())[:5])}...")

        # 快速连续发送请求
        results = []

        self.log("   📡 快速连续发送5个相同请求...")
        for i in range(5):
            result = self.send_single_request(payload, f"sync-{i}")
            results.append(result)
            time.sleep(0.05)  # 更短间隔增加竞态可能性

        # 打印所有响应
        self.log("   📥 收到响应:")
        for i, result in enumerate(results):
            if result.success:
                self.log(f"      请求{i+1}: '{result.output_content}' (时间: {result.execution_time:.3f}s, hash: {result.output_hash})")
            else:
                self.log(f"      请求{i+1}: ERROR - {result.output_content}")

        successful_results = [r for r in results if r.success]

        if len(successful_results) < 2:
            return {
                "sync_race_detected": False,
                "reason": "insufficient_successful_requests",
                "successful_count": len(successful_results)
            }

        # 检查输出一致性
        unique_outputs = set(r.output_hash for r in successful_results)
        unique_contents = set(r.output_content for r in successful_results)
        execution_times = [r.execution_time for r in successful_results]

        # 检查执行时间的异常变化
        time_variance = max(execution_times) - min(execution_times)

        # 详细分析
        self.log(f"   📊 同步竞态分析:")
        self.log(f"      成功请求: {len(successful_results)}")
        self.log(f"      唯一输出: {len(unique_outputs)}")
        self.log(f"      执行时间范围: {min(execution_times):.3f}s - {max(execution_times):.3f}s")
        self.log(f"      时间方差: {time_variance:.3f}s")

        race_detected = len(unique_outputs) > 1 or time_variance > 1.0

        if race_detected:
            self.log(f"   🚨 检测到同步竞态条件!", "CRITICAL")
            if len(unique_outputs) > 1:
                self.log(f"      证据1: 相同输入产生了 {len(unique_outputs)} 种不同输出")
                for i, content in enumerate(unique_contents):
                    self.log(f"         输出{i+1}: '{content}'")
            if time_variance > 1.0:
                self.log(f"      证据2: 执行时间异常变化 ({time_variance:.3f}s)")

        return {
            "sync_race_detected": race_detected,
            "unique_outputs": len(unique_outputs),
            "unique_contents": list(unique_contents),
            "execution_time_variance": time_variance,
            "execution_times": execution_times,
            "output_hashes": list(unique_outputs),
            "all_responses": [r.output_content for r in successful_results]
        }
    
    def run_comprehensive_logic_race_test(self):
        """运行全面的逻辑竞态条件测试"""
        self.log("🚀 开始vLLM逻辑竞态条件测试...")
        
        # 测试1: 异步张量竞态一致性
        self.log("\n=== 测试1: 异步张量竞态一致性 ===")
        consistency_results = self.test_async_tensor_race_consistency()
        
        # 测试2: logit_bias累加竞态
        self.log("\n=== 测试2: logit_bias累加竞态 ===")
        accumulation_results = self.test_logit_bias_accumulation_race()
        
        # 测试3: 设备同步竞态
        self.log("\n=== 测试3: 设备同步竞态 ===")
        sync_results = self.test_device_synchronization_race()
        
        # 生成报告
        self.generate_logic_race_report(consistency_results, accumulation_results, sync_results)
    
    def generate_logic_race_report(self, consistency_results: Dict, accumulation_results: Dict, sync_results: Dict):
        """生成逻辑竞态条件测试报告"""
        self.log("\n" + "="*70)
        self.log("vLLM 逻辑竞态条件测试报告")
        self.log("="*70)
        
        # 一致性测试结果
        self.log("\n📊 异步张量竞态一致性测试:")
        race_detected = False
        
        for test_name, result in consistency_results.items():
            if result.get("race_condition_detected", False):
                self.log(f"🚨 {test_name}: 检测到输出不一致!")
                self.log(f"   唯一输出数: {result['unique_outputs']}")
                self.log(f"   成功请求数: {result['successful_requests']}")
                race_detected = True
            else:
                self.log(f"✅ {test_name}: 输出一致")
        
        # 累加竞态测试结果
        self.log("\n📊 logit_bias累加竞态测试:")
        if accumulation_results.get("accumulation_race_detected", False):
            self.log("🚨 检测到logit_bias累加竞态条件!")
            self.log(f"   不同bias值产生了相同输出")
            race_detected = True
        else:
            self.log("✅ logit_bias累加正常")
        
        # 同步竞态测试结果
        self.log("\n📊 设备同步竞态测试:")
        if sync_results.get("sync_race_detected", False):
            self.log("🚨 检测到设备同步竞态条件!")
            self.log(f"   输出不一致或执行时间异常")
            race_detected = True
        else:
            self.log("✅ 设备同步正常")
        
        # 总结
        self.log("\n🔍 逻辑竞态条件总结:")
        if race_detected:
            self.log("🚨 发现逻辑竞态条件漏洞!")
            self.log("\n💥 实际影响:")
            self.log("- 相同输入可能产生不同输出")
            self.log("- 破坏模型推理的确定性")
            self.log("- 影响应用的可靠性和一致性")
            self.log("- 可能导致A/B测试结果不准确")
            
            self.log("\n🛠️  修复建议:")
            self.log("1. 在async_tensor_h2d后添加同步:")
            self.log("   torch.cuda.synchronize()")
            self.log("2. 使用原子操作处理logit_bias累加")
            self.log("3. 实施请求级别的锁机制")
            self.log("4. 确保异步操作的顺序一致性")
        else:
            self.log("✅ 未发现明显的逻辑竞态条件")
        
        self.log("\n💡 基于源代码的技术分析:")
        self.log("- async_tensor_h2d使用non_blocking=True")
        self.log("- index_put_可能在数据传输未完成时执行")
        self.log("- 多个并发请求共享GPU设备和内存")
        self.log("- 缺乏适当的同步机制确保操作顺序")

def main():
    parser = argparse.ArgumentParser(description='vLLM逻辑竞态条件测试工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    tester = VLLMLogicRaceTester(args.target, args.debug)
    tester.run_comprehensive_logic_race_test()

if __name__ == "__main__":
    main()
