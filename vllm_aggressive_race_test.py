#!/usr/bin/env python3
"""
vLLM 激进竞态条件测试
====================

使用更激进的方法尝试触发竞态条件：
1. 极高并发度
2. 大量logit_bias
3. 快速时序切换
4. 混合不同类型的请求
"""

import requests
import json
import time
import threading
import concurrent.futures
from typing import List, Dict, Any
import argparse

class AggressiveRaceTester:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "max_tokens": 20,
            "temperature": 0.0,
            "seed": 42
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def send_request(self, payload: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """发送请求"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                resp_data = response.json()
                content = resp_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                return {
                    "request_id": request_id,
                    "success": True,
                    "content": content,
                    "execution_time": execution_time,
                    "timestamp": start_time,
                    "logit_bias": payload.get("logit_bias", {})
                }
            else:
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "execution_time": execution_time,
                    "logit_bias": payload.get("logit_bias", {})
                }
                
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "logit_bias": payload.get("logit_bias", {})
            }
    
    def test_extreme_concurrency_race(self):
        """测试极高并发度的竞态条件"""
        self.log("🎯 测试极高并发度竞态条件")
        self.log("="*60)
        
        # 创建大量logit_bias的载荷
        large_bias = {str(i): float(i % 10 - 5) for i in range(200)}  # 200个token
        
        payload = {
            **self.base_payload,
            "messages": [{"role": "user", "content": "Count: 1, 2, 3"}],
            "logit_bias": large_bias
        }
        
        self.log(f"📤 使用包含{len(large_bias)}个token的logit_bias")
        self.log("🔄 启动50个并发线程...")
        
        results = []
        
        def worker(thread_id: int):
            result = self.send_request(payload, f"extreme-{thread_id}")
            results.append(result)
        
        # 启动50个并发线程
        with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(worker, i) for i in range(50)]
            concurrent.futures.wait(futures, timeout=120)
        
        # 分析结果
        self.analyze_extreme_concurrency_results(results)
    
    def analyze_extreme_concurrency_results(self, results: List[Dict]):
        """分析极高并发结果"""
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"\n📊 极高并发测试结果:")
        self.log(f"   总请求: {len(results)}")
        self.log(f"   成功请求: {len(successful_results)}")
        self.log(f"   失败请求: {len(results) - len(successful_results)}")
        
        if len(successful_results) < 10:
            self.log("❌ 成功请求太少，无法分析")
            return
        
        # 分析输出一致性
        contents = [r['content'] for r in successful_results]
        unique_contents = set(contents)
        
        self.log(f"   唯一输出: {len(unique_contents)}")
        
        for i, content in enumerate(unique_contents):
            count = contents.count(content)
            self.log(f"      输出{i+1}: '{content}' (出现{count}次)")
        
        # 分析执行时间
        execution_times = [r['execution_time'] for r in successful_results]
        min_time = min(execution_times)
        max_time = max(execution_times)
        avg_time = sum(execution_times) / len(execution_times)
        
        self.log(f"   执行时间: {min_time:.3f}s - {max_time:.3f}s (平均: {avg_time:.3f}s)")
        self.log(f"   时间方差: {max_time - min_time:.3f}s")
        
        # 检查竞态条件
        if len(unique_contents) > 1:
            self.log("🚨 发现极高并发竞态条件!", "CRITICAL")
            self.log("💥 相同输入在高并发下产生了不同输出")
            return True
        
        if max_time - min_time > 2.0:
            self.log("🚨 发现执行时间异常!", "CRITICAL")
            self.log("💥 高并发导致执行时间严重不一致")
            return True
        
        return False
    
    def test_rapid_bias_switching_race(self):
        """测试快速bias切换竞态条件"""
        self.log("\n🎯 测试快速bias切换竞态条件")
        self.log("="*60)
        
        # 创建对立的bias配置
        bias_configs = [
            {str(i): 10.0 for i in range(10, 20)},      # 偏向10-19
            {str(i): -10.0 for i in range(10, 20)},     # 避免10-19
            {str(i): 5.0 for i in range(20, 30)},       # 偏向20-29
            {str(i): -5.0 for i in range(20, 30)},      # 避免20-29
        ]
        
        self.log("🔄 快速切换4种对立的bias配置...")
        
        results = []
        
        # 快速连续发送40个请求，每10个使用一种配置
        for i in range(40):
            bias_idx = i % 4
            payload = {
                **self.base_payload,
                "messages": [{"role": "user", "content": f"Generate text {i}:"}],
                "logit_bias": bias_configs[bias_idx]
            }
            
            result = self.send_request(payload, f"rapid-{i}")
            results.append((bias_idx, result))
            
            time.sleep(0.01)  # 10ms间隔，极快切换
        
        # 分析快速切换结果
        self.analyze_rapid_switching_results(results, bias_configs)
    
    def analyze_rapid_switching_results(self, results: List[tuple], bias_configs: List[Dict]):
        """分析快速切换结果"""
        self.log(f"\n📊 快速切换结果分析:")
        
        # 按bias配置分组
        groups = {0: [], 1: [], 2: [], 3: []}
        
        for bias_idx, result in results:
            if result.get("success", False):
                groups[bias_idx].append(result)
        
        # 分析每组的一致性
        self.log("   各配置组结果:")
        group_outputs = {}
        
        for bias_idx, group_results in groups.items():
            if not group_results:
                continue
            
            contents = [r['content'] for r in group_results]
            unique_contents = set(contents)
            group_outputs[bias_idx] = unique_contents
            
            self.log(f"      配置{bias_idx}: {len(group_results)}个请求, {len(unique_contents)}种输出")
            
            # 如果同一配置产生了不同输出
            if len(unique_contents) > 1:
                self.log(f"🚨 配置{bias_idx}内部不一致!", "CRITICAL")
                for content in unique_contents:
                    count = contents.count(content)
                    self.log(f"         '{content}' ({count}次)")
        
        # 检查对立配置是否产生了相同输出
        for i in range(len(bias_configs)):
            for j in range(i+1, len(bias_configs)):
                if i in group_outputs and j in group_outputs:
                    common_outputs = group_outputs[i].intersection(group_outputs[j])
                    if common_outputs:
                        self.log(f"🚨 对立配置{i}和{j}产生了相同输出!", "CRITICAL")
                        self.log(f"   相同输出: {list(common_outputs)}")
    
    def test_mixed_request_interference(self):
        """测试混合请求类型的相互干扰"""
        self.log("\n🎯 测试混合请求类型干扰")
        self.log("="*60)
        
        # 创建不同类型的请求
        request_types = [
            {
                "name": "大bias",
                "payload": {
                    **self.base_payload,
                    "messages": [{"role": "user", "content": "Say hello"}],
                    "logit_bias": {str(i): float(i % 20 - 10) for i in range(100)}
                }
            },
            {
                "name": "小bias", 
                "payload": {
                    **self.base_payload,
                    "messages": [{"role": "user", "content": "Say hello"}],
                    "logit_bias": {"10": 1.0, "20": -1.0}
                }
            },
            {
                "name": "无bias",
                "payload": {
                    **self.base_payload,
                    "messages": [{"role": "user", "content": "Say hello"}],
                    "logit_bias": {}
                }
            }
        ]
        
        self.log("🔄 同时发送3种不同类型的请求...")
        
        results = []
        
        def worker(request_type_idx: int, request_id: int):
            request_type = request_types[request_type_idx]
            result = self.send_request(request_type["payload"], f"mixed-{request_type['name']}-{request_id}")
            results.append((request_type_idx, result))
        
        # 使用线程池同时发送不同类型的请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=15) as executor:
            futures = []
            
            # 每种类型发送5个请求
            for request_type_idx in range(3):
                for request_id in range(5):
                    future = executor.submit(worker, request_type_idx, request_id)
                    futures.append(future)
            
            concurrent.futures.wait(futures, timeout=60)
        
        # 分析混合请求结果
        self.analyze_mixed_request_results(results, request_types)
    
    def analyze_mixed_request_results(self, results: List[tuple], request_types: List[Dict]):
        """分析混合请求结果"""
        self.log(f"\n📊 混合请求干扰分析:")
        
        # 按请求类型分组
        type_groups = {0: [], 1: [], 2: []}
        
        for type_idx, result in results:
            if result.get("success", False):
                type_groups[type_idx].append(result)
        
        # 分析每种类型的结果
        type_outputs = {}
        
        for type_idx, group_results in type_groups.items():
            if not group_results:
                continue
            
            type_name = request_types[type_idx]["name"]
            contents = [r['content'] for r in group_results]
            unique_contents = set(contents)
            type_outputs[type_idx] = unique_contents
            
            self.log(f"   {type_name}: {len(group_results)}个请求, {len(unique_contents)}种输出")
            
            for content in unique_contents:
                count = contents.count(content)
                self.log(f"      '{content}' ({count}次)")
            
            # 检查内部一致性
            if len(unique_contents) > 1:
                self.log(f"🚨 {type_name}内部不一致!", "CRITICAL")
        
        # 检查不同类型是否有异常的相似性
        for i in range(len(request_types)):
            for j in range(i+1, len(request_types)):
                if i in type_outputs and j in type_outputs:
                    common_outputs = type_outputs[i].intersection(type_outputs[j])
                    if common_outputs:
                        type1_name = request_types[i]["name"]
                        type2_name = request_types[j]["name"]
                        self.log(f"⚠️  {type1_name}和{type2_name}有相同输出: {list(common_outputs)}")
    
    def run_aggressive_race_test(self):
        """运行激进的竞态条件测试"""
        self.log("🚀 vLLM 激进竞态条件测试")
        self.log("="*70)
        self.log("目标: 使用极端条件触发竞态条件")
        self.log("="*70)
        
        # 测试1: 极高并发
        race1 = self.test_extreme_concurrency_race()
        
        # 测试2: 快速bias切换
        self.test_rapid_bias_switching_race()
        
        # 测试3: 混合请求干扰
        self.test_mixed_request_interference()
        
        self.log("\n" + "="*70)
        self.log("🎯 激进竞态条件测试总结")
        self.log("="*70)
        self.log("如果在极端条件下仍未发现竞态条件，可能说明:")
        self.log("1. ✅ vLLM的异步操作实现较为稳定")
        self.log("2. ✅ 当前版本可能已修复相关问题")
        self.log("3. 🤔 需要更特定的触发条件")
        self.log("4. 🤔 竞态条件可能存在于其他代码路径")

def main():
    parser = argparse.ArgumentParser(description='vLLM激进竞态条件测试工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    
    args = parser.parse_args()
    
    tester = AggressiveRaceTester(args.target)
    tester.run_aggressive_race_test()

if __name__ == "__main__":
    main()
