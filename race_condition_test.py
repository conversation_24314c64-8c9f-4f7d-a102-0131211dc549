#!/usr/bin/env python3
"""
vLLM 竞态条件漏洞测试
====================

专门测试异步张量操作中的竞态条件，分析实际效果。
"""

import requests
import json
import time
import threading
import concurrent.futures
from typing import List, Dict, Any
import statistics

class RaceConditionTester:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip('/')
        self.results = []
        self.lock = threading.Lock()
        
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 1,
            "temperature": 0.1
        }
    
    def log(self, message: str):
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {message}")
    
    def test_async_tensor_race(self) -> Dict[str, Any]:
        """测试异步张量操作竞态条件"""
        self.log("🎯 开始异步张量竞态条件测试...")
        
        # 构造大量有效的logit_bias (在词汇表范围内)
        # 词汇表大小是151936，我们使用0-1000范围内的token
        large_bias = {str(i): float(i % 10) for i in range(0, 1000)}
        
        payload = {
            **self.base_payload,
            "logit_bias": large_bias
        }
        
        # 测试参数
        num_threads = 10
        requests_per_thread = 5
        
        self.log(f"启动 {num_threads} 个线程，每个线程发送 {requests_per_thread} 个请求")
        
        def send_concurrent_request(thread_id: int, request_id: int):
            """发送并发请求"""
            session = requests.Session()
            session.headers.update({'Content-Type': 'application/json'})
            
            try:
                start_time = time.time()
                response = session.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=30
                )
                end_time = time.time()
                
                result = {
                    "thread_id": thread_id,
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "execution_time": end_time - start_time,
                    "response_size": len(response.content),
                    "success": response.status_code == 200,
                    "timestamp": start_time
                }
                
                # 检查响应内容
                if response.status_code == 200:
                    try:
                        resp_data = response.json()
                        result["has_content"] = bool(resp_data.get("choices", [{}])[0].get("message", {}).get("content"))
                    except:
                        result["has_content"] = False
                else:
                    result["error_message"] = response.text[:200]
                
                with self.lock:
                    self.results.append(result)
                    
            except requests.exceptions.Timeout:
                with self.lock:
                    self.results.append({
                        "thread_id": thread_id,
                        "request_id": request_id,
                        "status_code": "TIMEOUT",
                        "execution_time": 30.0,
                        "success": False,
                        "timestamp": time.time()
                    })
            except Exception as e:
                with self.lock:
                    self.results.append({
                        "thread_id": thread_id,
                        "request_id": request_id,
                        "status_code": "ERROR",
                        "error": str(e),
                        "success": False,
                        "timestamp": time.time()
                    })
        
        # 使用ThreadPoolExecutor进行并发测试
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = []
            
            # 提交所有任务
            for thread_id in range(num_threads):
                for request_id in range(requests_per_thread):
                    future = executor.submit(send_concurrent_request, thread_id, request_id)
                    futures.append(future)
            
            # 等待所有任务完成
            concurrent.futures.wait(futures, timeout=60)
        
        return self.analyze_race_condition_results()
    
    def analyze_race_condition_results(self) -> Dict[str, Any]:
        """分析竞态条件测试结果"""
        self.log("📊 分析竞态条件测试结果...")
        
        if not self.results:
            return {"error": "No results collected"}
        
        # 基本统计
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r.get("success", False))
        failed_requests = total_requests - successful_requests
        
        # 执行时间统计
        execution_times = [r.get("execution_time", 0) for r in self.results if "execution_time" in r]
        
        # 状态码统计
        status_codes = {}
        for result in self.results:
            status = result.get("status_code", "UNKNOWN")
            status_codes[status] = status_codes.get(status, 0) + 1
        
        # 时间序列分析 - 检测异常模式
        timestamps = [r.get("timestamp", 0) for r in self.results]
        if timestamps:
            time_span = max(timestamps) - min(timestamps)
            request_rate = total_requests / max(time_span, 1)
        else:
            time_span = 0
            request_rate = 0
        
        # 检测竞态条件的指标
        race_condition_indicators = []
        
        # 1. 异常高的失败率
        failure_rate = failed_requests / total_requests
        if failure_rate > 0.3:  # 30%以上失败率
            race_condition_indicators.append(f"高失败率: {failure_rate:.2%}")
        
        # 2. 执行时间方差过大
        if execution_times and len(execution_times) > 1:
            time_std = statistics.stdev(execution_times)
            time_mean = statistics.mean(execution_times)
            if time_std > time_mean * 0.5:  # 标准差超过均值的50%
                race_condition_indicators.append(f"执行时间方差过大: std={time_std:.2f}, mean={time_mean:.2f}")
        
        # 3. 超时请求
        timeout_count = sum(1 for r in self.results if r.get("status_code") == "TIMEOUT")
        if timeout_count > 0:
            race_condition_indicators.append(f"超时请求: {timeout_count}")
        
        # 4. 服务器错误
        server_errors = sum(1 for r in self.results if isinstance(r.get("status_code"), int) and r.get("status_code") >= 500)
        if server_errors > 0:
            race_condition_indicators.append(f"服务器错误: {server_errors}")
        
        # 5. 响应内容不一致
        content_responses = [r for r in self.results if r.get("success") and "has_content" in r]
        if content_responses:
            content_rate = sum(1 for r in content_responses if r["has_content"]) / len(content_responses)
            if content_rate < 0.8:  # 80%以下的响应有内容
                race_condition_indicators.append(f"响应内容不一致率: {1-content_rate:.2%}")
        
        analysis = {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "failure_rate": failure_rate,
            "status_codes": status_codes,
            "execution_times": {
                "min": min(execution_times) if execution_times else 0,
                "max": max(execution_times) if execution_times else 0,
                "mean": statistics.mean(execution_times) if execution_times else 0,
                "std": statistics.stdev(execution_times) if len(execution_times) > 1 else 0
            },
            "time_span": time_span,
            "request_rate": request_rate,
            "race_condition_indicators": race_condition_indicators,
            "race_condition_detected": len(race_condition_indicators) > 0
        }
        
        return analysis
    
    def test_memory_pressure_race(self) -> Dict[str, Any]:
        """测试内存压力下的竞态条件"""
        self.log("🎯 测试内存压力下的竞态条件...")
        
        # 构造更大的logit_bias来增加内存压力
        memory_pressure_bias = {str(i): float(i % 100) for i in range(0, 5000)}  # 5000个token
        
        payload = {
            **self.base_payload,
            "logit_bias": memory_pressure_bias
        }
        
        def send_memory_pressure_request():
            try:
                start_time = time.time()
                response = requests.post(
                    f"{self.target_url}/v1/chat/completions",
                    json=payload,
                    timeout=45
                )
                end_time = time.time()
                
                return {
                    "status_code": response.status_code,
                    "execution_time": end_time - start_time,
                    "success": response.status_code == 200
                }
            except Exception as e:
                return {
                    "status_code": "ERROR",
                    "error": str(e),
                    "success": False
                }
        
        # 发送3个并发的内存压力请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(send_memory_pressure_request) for _ in range(3)]
            results = [future.result() for future in concurrent.futures.as_completed(futures, timeout=60)]
        
        return {
            "memory_pressure_results": results,
            "all_successful": all(r.get("success", False) for r in results),
            "max_execution_time": max(r.get("execution_time", 0) for r in results),
            "any_errors": any(r.get("status_code") == "ERROR" for r in results)
        }
    
    def run_comprehensive_race_test(self):
        """运行全面的竞态条件测试"""
        self.log("🚀 开始全面竞态条件测试...")
        
        # 测试1: 异步张量竞态条件
        race_results = self.test_async_tensor_race()
        
        # 测试2: 内存压力竞态条件
        memory_results = self.test_memory_pressure_race()
        
        # 生成报告
        self.generate_race_condition_report(race_results, memory_results)
    
    def generate_race_condition_report(self, race_results: Dict, memory_results: Dict):
        """生成竞态条件测试报告"""
        self.log("\n" + "="*60)
        self.log("vLLM 竞态条件测试报告")
        self.log("="*60)
        
        # 异步张量竞态条件结果
        self.log("\n📊 异步张量竞态条件测试结果:")
        if race_results.get("race_condition_detected"):
            self.log("🚨 检测到竞态条件指标:")
            for indicator in race_results["race_condition_indicators"]:
                self.log(f"   - {indicator}")
        else:
            self.log("✅ 未检测到明显的竞态条件")
        
        self.log(f"总请求数: {race_results.get('total_requests', 0)}")
        self.log(f"成功率: {(1-race_results.get('failure_rate', 0)):.2%}")
        self.log(f"平均执行时间: {race_results.get('execution_times', {}).get('mean', 0):.2f}秒")
        
        # 内存压力测试结果
        self.log("\n📊 内存压力竞态条件测试结果:")
        if memory_results.get("all_successful"):
            self.log("✅ 所有内存压力请求成功")
        else:
            self.log("🚨 内存压力测试中有失败请求")
        
        self.log(f"最大执行时间: {memory_results.get('max_execution_time', 0):.2f}秒")
        
        # 竞态条件效果分析
        self.log("\n🔍 竞态条件效果分析:")
        self.log("竞态条件可能导致的效果:")
        self.log("1. 🔄 请求处理不一致 - 相同请求得到不同结果")
        self.log("2. ⏱️  执行时间波动 - 异步操作同步问题")
        self.log("3. 💾 内存泄露 - 异步张量未正确释放")
        self.log("4. 🚫 请求失败 - 资源竞争导致处理失败")
        self.log("5. 🔒 死锁风险 - 多个异步操作相互等待")

def main():
    target_url = "http://47.253.15.203:8901"
    tester = RaceConditionTester(target_url)
    tester.run_comprehensive_race_test()

if __name__ == "__main__":
    main()
